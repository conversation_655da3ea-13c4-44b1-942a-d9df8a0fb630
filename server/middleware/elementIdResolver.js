const mongoose = require('mongoose');
const redis = require('redis');
const { LRUCache } = require('lru-cache');

// Redis client for caching (optional - falls back gracefully if not available)
let redisClient = null;
try {
  if (process.env.REDIS_URL || process.env.NODE_ENV === 'production') {
    redisClient = redis.createClient(process.env.REDIS_URL);
    redisClient.on('error', (err) => {
      console.warn('Redis connection error, falling back to database-only:', err.message);
      redisClient = null;
    });
  }
} catch (error) {
  console.warn('Redis not available, using database-only caching:', error.message);
}

// In-memory LRU cache for ObjectID→Group validation (fallback when Redis unavailable)
const objectIdGroupCache = new LRUCache({
  max: 1000,           // Maximum 1000 cached entries
  ttl: 1000 * 60 * 5   // 5 minute TTL
});

// In-memory LRU cache for ElementID→ObjectID resolution
const elementIdCache = new LRUCache({
  max: 500,            // Maximum 500 cached entries
  ttl: 1000 * 60 * 2   // 2 minute TTL (shorter since ElementIDs change less frequently)
});
const { validateElementId } = require('../utils/elementIdGenerator');
const cacheMetrics = require('../utils/cacheMetrics');

/**
 * Cache helper functions
 */
async function getCachedObjectIdGroup(objectId, modelName) {
  const cacheKey = `objectid:${objectId}:${modelName}:group`;

  try {
    // Try Redis first
    if (redisClient) {
      const cached = await redisClient.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
    }

    // Fall back to in-memory cache
    const memCached = objectIdGroupCache.get(cacheKey);
    if (memCached) {
      return memCached;
    }
  } catch (error) {
    console.warn('Cache read error:', error.message);
  }

  return null;
}

async function setCachedObjectIdGroup(objectId, modelName, groupId, exists = true) {
  const cacheKey = `objectid:${objectId}:${modelName}:group`;
  const cacheValue = { groupId, exists, timestamp: Date.now() };

  try {
    // Set in Redis with TTL
    if (redisClient) {
      await redisClient.setEx(cacheKey, 300, JSON.stringify(cacheValue)); // 5 min TTL
    }

    // Set in memory cache as backup
    objectIdGroupCache.set(cacheKey, cacheValue);
  } catch (error) {
    console.warn('Cache write error:', error.message);
  }
}

async function getCachedElementId(elementId, modelName) {
  const cacheKey = `elementid:${elementId}:${modelName}`;

  try {
    // Try Redis first
    if (redisClient) {
      const cached = await redisClient.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
    }

    // Fall back to in-memory cache
    const memCached = elementIdCache.get(cacheKey);
    if (memCached) {
      return memCached;
    }
  } catch (error) {
    console.warn('Cache read error:', error.message);
  }

  return null;
}

async function setCachedElementId(elementId, modelName, objectId, groupId) {
  const cacheKey = `elementid:${elementId}:${modelName}`;
  const cacheValue = { objectId, groupId, timestamp: Date.now() };

  try {
    // Set in Redis with TTL
    if (redisClient) {
      await redisClient.setEx(cacheKey, 120, JSON.stringify(cacheValue)); // 2 min TTL
    }

    // Set in memory cache as backup
    elementIdCache.set(cacheKey, cacheValue);
  } catch (error) {
    console.warn('Cache write error:', error.message);
  }
}

/**
 * Middleware to resolve ElementID to ObjectID
 * Supports both ElementID and ObjectID for backward compatibility
 * @param {string} modelName - The model name ('Project', 'Feature', 'Requirement')
 * @returns {Function} - Express middleware function
 */
function createElementIdResolver(modelName) {
  return async (req, res, next) => {
    try {
      // Support different parameter names: id, projectId, featureId, requirementId
      const id = req.params.id ||
                 req.params.projectId ||
                 req.params.featureId ||
                 req.params.requirementId;

      if (!id) {
        return res.status(400).json({ message: 'ID parameter is required' });
      }
      
      // Check if it's a valid ObjectId (backward compatibility)
      if (mongoose.Types.ObjectId.isValid(id) && id.length === 24) {
        // For ObjectID: Still need to verify group access for security

        // Determine user's group for caching and filtering
        let userGroupId = null;
        if (req.user && req.user.group) {
          userGroupId = req.user.group;
        } else if (req.userGroup) {
          userGroupId = req.userGroup._id;
        }

        // Skip group filtering for super users
        if (req.isSuperUser) {
          userGroupId = null;
        }

        // Try cache first (only if we have a group context)
        if (userGroupId) {
          const cacheStart = Date.now();
          const cached = await getCachedObjectIdGroup(id, modelName);
          const cacheTime = Date.now() - cacheStart;

          if (cached) {
            cacheMetrics.recordCacheHit('objectIdCache', cacheTime);
            // Check if cached entry matches user's group
            if (cached.exists && cached.groupId === userGroupId.toString()) {
              req.resolvedId = id;
              req.idType = 'objectId';
              return next();
            } else if (!cached.exists || cached.groupId !== userGroupId.toString()) {
              return res.status(404).json({ message: `${modelName} not found` });
            }
          } else {
            cacheMetrics.recordCacheMiss('objectIdCache', cacheTime);
          }
        }

        // Cache miss - query database
        const Model = mongoose.model(modelName);
        const filter = { _id: id };

        // Add group filter for multi-tenancy (unless super user)
        if (userGroupId) {
          filter.group = userGroupId;
        }

        // Use lean() for performance since we only need validation
        const dbStart = Date.now();
        const element = await Model.findOne(filter).select('_id group').lean();
        const dbTime = Date.now() - dbStart;
        cacheMetrics.recordDatabaseQuery('objectIdQueries', dbTime);

        if (!element) {
          // Cache the negative result to avoid repeated database queries
          if (userGroupId) {
            await setCachedObjectIdGroup(id, modelName, userGroupId.toString(), false);
          }
          return res.status(404).json({ message: `${modelName} not found` });
        }

        // Cache the positive result
        if (userGroupId) {
          await setCachedObjectIdGroup(id, modelName, element.group.toString(), true);
        }

        req.resolvedId = id;
        req.idType = 'objectId';
        return next();
      }
      
      // Check if it's a valid ElementID format
      if (!validateElementId(id)) {
        return res.status(400).json({ message: 'Invalid ID format' });
      }

      // Determine user's group for caching and filtering
      let userGroupId = null;
      if (req.user && req.user.group) {
        userGroupId = req.user.group;
      } else if (req.userGroup) {
        userGroupId = req.userGroup._id;
      }

      // Skip group filtering for super users
      if (req.isSuperUser) {
        userGroupId = null;
      }

      // Try cache first
      const cacheStart = Date.now();
      const cached = await getCachedElementId(id, modelName);
      const cacheTime = Date.now() - cacheStart;

      if (cached) {
        cacheMetrics.recordCacheHit('elementIdCache', cacheTime);
        // Check if cached entry matches user's group (or if super user)
        if (!userGroupId || cached.groupId === userGroupId.toString()) {
          req.resolvedId = cached.objectId;
          req.idType = 'elementId';
          return next();
        }
      } else {
        cacheMetrics.recordCacheMiss('elementIdCache', cacheTime);
      }

      // Cache miss - query database
      const Model = mongoose.model(modelName);
      const filter = { elementId: id };

      // Add group filter for multi-tenancy (if user context available)
      if (userGroupId) {
        filter.group = userGroupId;
      }

      // Use lean() for performance and get both _id and group for caching
      const dbStart = Date.now();
      const element = await Model.findOne(filter).select('_id group').lean();
      const dbTime = Date.now() - dbStart;
      cacheMetrics.recordDatabaseQuery('elementIdQueries', dbTime);
      
      if (!element) {
        return res.status(404).json({ message: `${modelName} not found` });
      }

      // Cache the result for future requests
      await setCachedElementId(id, modelName, element._id.toString(), element.group.toString());

      // Store resolved ObjectID for use in route handlers
      req.resolvedId = element._id.toString();
      req.idType = 'elementId';
      req.originalElementId = id;

      next();
    } catch (error) {
      console.error('ElementID resolution error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  };
}

/**
 * Specific resolvers for each model type
 */
const resolveProjectId = createElementIdResolver('Project');
const resolveFeatureId = createElementIdResolver('Feature');
const resolveRequirementId = createElementIdResolver('Requirement');

/**
 * Resolver for requirementId parameter specifically
 */
const resolveRequirementIdParam = async (req, res, next) => {
  try {
    const { requirementId } = req.params;

    if (!requirementId) {
      return res.status(400).json({ message: 'RequirementId parameter is required' });
    }

    // Check if it's a valid ObjectId (backward compatibility)
    if (mongoose.Types.ObjectId.isValid(requirementId) && requirementId.length === 24) {
      // For ObjectID: Still need to verify group access for security
      const Requirement = mongoose.model('Requirement');

      // Build query filter with group context
      const filter = { _id: requirementId };

      // Add group filter for multi-tenancy (unless super user)
      if (req.user && req.user.group) {
        filter.group = req.user.group;
      } else if (req.userGroup) {
        filter.group = req.userGroup._id;
      }

      // Skip group filtering for super users
      if (req.isSuperUser) {
        delete filter.group;
      }

      // Verify the ObjectID exists and belongs to the user's group
      const requirement = await Requirement.findOne(filter).select('_id');

      if (!requirement) {
        return res.status(404).json({ message: 'Requirement not found' });
      }

      req.resolvedRequirementId = requirementId;
      req.idType = 'objectId';
      return next();
    }

    // Check if it's a valid ElementID format
    if (!validateElementId(requirementId)) {
      return res.status(400).json({ message: `Invalid RequirementId format: ${requirementId}` });
    }

    // Get the Requirement model
    const Requirement = mongoose.model('Requirement');

    // Build query filter
    const filter = { elementId: requirementId };

    // Add group filter for multi-tenancy (if user context available)
    if (req.user && req.user.group) {
      filter.group = req.user.group;
    } else if (req.userGroup) {
      filter.group = req.userGroup._id;
    }

    // Skip group filtering for super users
    if (req.isSuperUser) {
      delete filter.group;
    }

    // Find the requirement by ElementID
    const requirement = await Requirement.findOne(filter).select('_id');

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    req.resolvedRequirementId = requirement._id;
    req.idType = 'elementId';
    next();
  } catch (error) {
    console.error('Error resolving RequirementId:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

/**
 * Generic resolver that can handle any model type
 * @param {string} modelName - The model name
 * @param {string} paramName - The parameter name (default: 'id')
 */
function resolveElementId(modelName, paramName = 'id') {
  return async (req, res, next) => {
    try {
      const id = req.params[paramName];
      
      if (!id) {
        return res.status(400).json({ message: `${paramName} parameter is required` });
      }
      
      // Check if it's a valid ObjectId (backward compatibility)
      if (mongoose.Types.ObjectId.isValid(id) && id.length === 24) {
        req[`resolved${paramName.charAt(0).toUpperCase() + paramName.slice(1)}`] = id;
        return next();
      }
      
      // Check if it's a valid ElementID format
      if (!validateElementId(id)) {
        return res.status(400).json({ message: `Invalid ${paramName} format` });
      }
      
      // Get the model
      const Model = mongoose.model(modelName);
      
      // Build query filter
      const filter = { elementId: id };
      
      // Add group filter for multi-tenancy
      if (req.user && req.user.group) {
        filter.group = req.user.group;
      } else if (req.userGroup) {
        filter.group = req.userGroup._id;
      }
      
      // Find the element by ElementID
      const element = await Model.findOne(filter).select('_id');
      
      if (!element) {
        return res.status(404).json({ message: `${modelName} not found` });
      }
      
      // Store resolved ObjectID
      req[`resolved${paramName.charAt(0).toUpperCase() + paramName.slice(1)}`] = element._id.toString();
      
      next();
    } catch (error) {
      console.error(`${modelName} ID resolution error:`, error);
      res.status(500).json({ message: 'Internal server error' });
    }
  };
}

module.exports = {
  createElementIdResolver,
  resolveProjectId,
  resolveFeatureId,
  resolveRequirementId,
  resolveRequirementIdParam,
  resolveElementId
};

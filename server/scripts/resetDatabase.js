const mongoose = require('mongoose');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Import models
const Project = require('../models/Project');
const Feature = require('../models/Feature');
const Requirement = require('../models/Requirement');
const TimelineEvent = require('../models/TimelineEvent');
const ArchivedTimelineEvent = require('../models/ArchivedTimelineEvent');
const User = require('../models/User');
const Group = require('../models/Group');

async function resetDatabase() {
  try {
    console.log('🔄 Starting database reset...');
    
    // Connect to MongoDB Atlas
    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      throw new Error('MONGODB_URI not found in environment variables');
    }

    console.log('📡 Connecting to MongoDB Atlas...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB Atlas');

    // Get counts before deletion
    const beforeCounts = {
      projects: await Project.countDocuments(),
      features: await Feature.countDocuments(),
      requirements: await Requirement.countDocuments(),
      timelineEvents: await TimelineEvent.countDocuments(),
      archivedEvents: await ArchivedTimelineEvent.countDocuments(),
      users: await User.countDocuments(),
      groups: await Group.countDocuments()
    };

    console.log('📊 Current database state:');
    console.log(`  Projects: ${beforeCounts.projects}`);
    console.log(`  Features: ${beforeCounts.features}`);
    console.log(`  Requirements: ${beforeCounts.requirements}`);
    console.log(`  Timeline Events: ${beforeCounts.timelineEvents}`);
    console.log(`  Archived Events: ${beforeCounts.archivedEvents}`);
    console.log(`  Users: ${beforeCounts.users}`);
    console.log(`  Groups: ${beforeCounts.groups}`);

    console.log('\n🗑️  Deleting data (preserving Users and Groups)...');

    // Delete all data except Users and Groups
    const deletionResults = await Promise.all([
      Project.deleteMany({}),
      Feature.deleteMany({}),
      Requirement.deleteMany({}),
      TimelineEvent.deleteMany({}),
      ArchivedTimelineEvent.deleteMany({})
    ]);

    console.log('✅ Deletion completed:');
    console.log(`  Projects deleted: ${deletionResults[0].deletedCount}`);
    console.log(`  Features deleted: ${deletionResults[1].deletedCount}`);
    console.log(`  Requirements deleted: ${deletionResults[2].deletedCount}`);
    console.log(`  Timeline Events deleted: ${deletionResults[3].deletedCount}`);
    console.log(`  Archived Events deleted: ${deletionResults[4].deletedCount}`);

    // Verify final state
    const afterCounts = {
      projects: await Project.countDocuments(),
      features: await Feature.countDocuments(),
      requirements: await Requirement.countDocuments(),
      timelineEvents: await TimelineEvent.countDocuments(),
      archivedEvents: await ArchivedTimelineEvent.countDocuments(),
      users: await User.countDocuments(),
      groups: await Group.countDocuments()
    };

    console.log('\n📊 Final database state:');
    console.log(`  Projects: ${afterCounts.projects}`);
    console.log(`  Features: ${afterCounts.features}`);
    console.log(`  Requirements: ${afterCounts.requirements}`);
    console.log(`  Timeline Events: ${afterCounts.timelineEvents}`);
    console.log(`  Archived Events: ${afterCounts.archivedEvents}`);
    console.log(`  Users: ${afterCounts.users} (preserved)`);
    console.log(`  Groups: ${afterCounts.groups} (preserved)`);

    // Update group retention policies to include new timeline fields
    console.log('\n🔧 Updating group retention policies...');
    const groups = await Group.find({});
    let updatedGroups = 0;

    for (const group of groups) {
      let needsUpdate = false;

      // Initialize retention policy if it doesn't exist
      if (!group.retentionPolicy) {
        group.retentionPolicy = {};
        needsUpdate = true;
      }

      // Set default values for new fields
      if (!group.retentionPolicy.tier) {
        if (group.maxUsers <= 3) group.retentionPolicy.tier = 'small';
        else if (group.maxUsers <= 25) group.retentionPolicy.tier = 'medium';
        else if (group.maxUsers <= 100) group.retentionPolicy.tier = 'large';
        else group.retentionPolicy.tier = 'enterprise';
        needsUpdate = true;
      }

      if (!group.retentionPolicy.retentionMonths) {
        const tierMonths = {
          small: 12, medium: 24, large: 36, enterprise: 60
        };
        group.retentionPolicy.retentionMonths = tierMonths[group.retentionPolicy.tier] || 12;
        needsUpdate = true;
      }

      if (!group.retentionPolicy.expirationWarningDays) {
        group.retentionPolicy.expirationWarningDays = [30, 14, 7, 1];
        needsUpdate = true;
      }

      if (!group.retentionPolicy.purchaseMoreTimeUrl) {
        group.retentionPolicy.purchaseMoreTimeUrl = '/admin/extend-retention';
        needsUpdate = true;
      }

      if (group.retentionPolicy.archivalEnabled === undefined) {
        group.retentionPolicy.archivalEnabled = false; // Disabled by default
        needsUpdate = true;
      }

      if (!group.retentionPolicy.archivalFrequency) {
        group.retentionPolicy.archivalFrequency = 'monthly';
        needsUpdate = true;
      }

      if (needsUpdate) {
        await group.save();
        updatedGroups++;
      }
    }

    console.log(`✅ Updated ${updatedGroups} groups with retention policies`);

    console.log('\n🎉 Database reset completed successfully!');
    console.log('📝 Summary:');
    console.log('  ✅ All projects, features, requirements deleted');
    console.log('  ✅ All timeline events and archived events deleted');
    console.log('  ✅ Users and groups preserved');
    console.log('  ✅ Group retention policies updated');
    console.log('  🚀 Ready for timeline system testing');

  } catch (error) {
    console.error('❌ Error during database reset:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('📡 Disconnected from MongoDB');
  }
}

// Run the reset if this script is executed directly
if (require.main === module) {
  resetDatabase()
    .then(() => {
      console.log('✅ Reset script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Reset script failed:', error);
      process.exit(1);
    });
}

module.exports = resetDatabase;

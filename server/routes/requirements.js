const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const { addGroupContext, filterByGroup } = require('../middleware/groupAuth');
const { resolveRequirementId, resolveRequirementIdParam, resolveElementId } = require('../middleware/elementIdResolver');
const { generateElementId } = require('../utils/elementIdGenerator');
const Requirement = require('../models/Requirement');
const User = require('../models/User');
const Project = require('../models/Project');
const Feature = require('../models/Feature');

// Get all requirements
router.get('/', auth, addGroupContext, filterByGroup, async (req, res) => {
  try {
    // Check if this is a navigation request (needs all data)
    const isNavigation = req.query.navigation === 'true';

    // Build base filter with group context
    let filter = req.isSuperUser ? {} : req.groupFilter;

    if (isNavigation) {
      // For navigation: return minimal data for all requirements (no pagination)
      const requirements = await Requirement.find(filter)
        .select('elementId project feature group type createdAt currentVersion versions.title versions.state versions.tasks members.user approvers.user approvals.user approvals.version')
        .populate('project', 'name')
        .populate('feature', 'name')
        .populate('members.user', '_id')
        .populate('approvers.user', '_id')
        .populate('approvals.user', '_id')
        .populate('versions.tasks.user', '_id')
        .sort({ createdAt: -1 });

      return res.json(requirements);
    }

    // For regular requests: use pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 100;
    const skip = (page - 1) * limit;

    // Add project filter if specified
    if (req.query.project) {
      filter.project = req.query.project;
    }

    const requirements = await Requirement.find(filter)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.tasks.user', 'username color firstName lastName avatar')
      .populate('project', 'name')
      .populate('feature', 'name')
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip);

    // Get total count for pagination info
    const total = await Requirement.countDocuments(filter);

    res.json({
      requirements,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    console.error('Error in GET /api/requirements:', err);
    res.status(500).json({
      message: 'Server Error',
      error: err.message
    });
  }
});

// Create a new requirement
router.post('/', auth, addGroupContext, async (req, res) => {
  try {
    const { project, feature, title, description, type } = req.body;

    // Resolve project ElementID to ObjectID if needed
    let resolvedProjectId = project;
    if (project && typeof project === 'string') {
      // Check if it's an ElementID (not a valid ObjectID)
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(project) || project.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const projectDoc = await Project.findOne({
          elementId: project,
          group: req.userGroup._id
        });
        if (!projectDoc) {
          return res.status(404).json({ message: 'Project not found' });
        }
        resolvedProjectId = projectDoc._id;
        console.log('Resolved project ElementID', project, 'to ObjectID', resolvedProjectId);
      }
    }

    // Resolve feature ElementID to ObjectID if needed
    let resolvedFeatureId = feature;
    if (feature && typeof feature === 'string') {
      // Check if it's an ElementID (not a valid ObjectID)
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(feature) || feature.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const featureDoc = await Feature.findOne({
          elementId: feature,
          group: req.userGroup._id
        });
        if (!featureDoc) {
          return res.status(404).json({ message: 'Feature not found' });
        }
        resolvedFeatureId = featureDoc._id;
        console.log('Resolved feature ElementID', feature, 'to ObjectID', resolvedFeatureId);
      }
    }

    // Get user's group context
    const user = await User.findById(req.user.userId).populate('group');
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Verify user has access to create requirements in this group
    if (!user.isSuperUser && user.groupStatus !== 'active') {
      return res.status(403).json({ message: 'Account is inactive' });
    }

    const requirement = new Requirement({
      project: resolvedProjectId,
      feature: resolvedFeatureId,
      group: user.group._id, // Set the group for multi-tenancy
      type: type || 'requirement',
      versions: [{
        version: 1,
        title,
        description,
        createdBy: req.user.userId
      }],
      createdBy: req.user.userId,
      members: []  // Don't automatically add the creator as a member
    });

    await requirement.save();

    // Generate ElementID after requirement is saved
    const elementId = await generateElementId(requirement.group, requirement.project, 'requirement');
    requirement.elementId = elementId;
    await requirement.save();

    // Auto-transition feature from NEW to OPEN when first requirement is added
    if (resolvedFeatureId) {
      const featureDoc = await Feature.findById(resolvedFeatureId);
      if (featureDoc && featureDoc.state === 'NEW') {
        try {
          await featureDoc.transitionState('OPEN');
        } catch (err) {
          console.error('Error auto-transitioning feature to OPEN:', err);
          // Don't fail the requirement creation if feature transition fails
        }
      }
    }

    // Populate the requirement with user data before sending response
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('approvers.user', 'username color firstName lastName avatar')
      .populate('approvals.user', 'username color firstName lastName avatar')
      .populate('feature', 'title');

    res.status(201).json(populatedRequirement);
  } catch (err) {
    console.error('Error creating requirement:', err);
    res.status(500).json({ message: err.message });
  }
});

// Get all requirements for a project
router.get('/project/:projectId', auth, addGroupContext, resolveElementId('Project', 'projectId'), async (req, res) => {
  try {
    console.log('Fetching requirements for project:', req.params.projectId);
    console.log('Resolved project ID:', req.resolvedProjectId);
    console.log('Query:', { project: req.resolvedProjectId });

    // First check if the project exists
    const project = await Project.findById(req.resolvedProjectId);
    if (!project) {
      console.log('Project not found:', req.params.projectId);
      return res.status(404).json({ message: 'Project not found' });
    }
    console.log('Project found:', project.name);

    // Get all requirements for the project with pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 200; // Higher limit for project view
    const skip = (page - 1) * limit;

    const requirements = await Requirement.find({ project: req.resolvedProjectId })
      .populate('createdBy', 'username firstName lastName avatar')
      .populate('versions.createdBy', 'username firstName lastName avatar')
      .populate('versions.comments.user', 'username firstName lastName avatar')
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip);

    console.log('Found requirements:', requirements.length);
    console.log('Requirements data:', JSON.stringify(requirements, null, 2));

    // Ensure we always return an array
    const response = Array.isArray(requirements) ? requirements : [];
    console.log('Sending response:', JSON.stringify(response, null, 2));
    res.json(response);
  } catch (error) {
    console.error('Error fetching requirements:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack
    });
    res.status(500).json({ message: error.message });
  }
});

// Get valid state transitions for a requirement version (MUST be before /:id route)
router.get('/:requirementId/valid-transitions', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const { version } = req.query;
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ msg: 'Requirement not found' });
    }

    const versionNumber = version ? parseInt(version) : requirement.currentVersion;

    // Handle case where requested version doesn't exist (due to cleanup)
    let versionData = requirement.versions.find(v => v.version === versionNumber);

    if (!versionData) {
      console.log(`Version ${versionNumber} not found, using current version ${requirement.currentVersion}`);
      // If requested version doesn't exist, use the current version
      versionData = requirement.versions.find(v => v.version === requirement.currentVersion);

      if (!versionData) {
        // If still no version found, use the last version in the array
        versionData = requirement.versions[requirement.versions.length - 1];
      }

      if (!versionData) {
        return res.status(404).json({ msg: 'No valid version found' });
      }
    }

    const validTransitions = Requirement.getValidTransitions(versionData.state);

    // Check if can transition to each state
    const canTransitionNext = validTransitions.next ?
      requirement.canTransitionState(versionData.version, validTransitions.next) :
      { canTransition: false, reason: 'No next state available' };

    const canTransitionPrevious = validTransitions.previous ?
      requirement.canTransitionState(versionData.version, validTransitions.previous) :
      { canTransition: false, reason: 'No previous state available' };

    res.json({
      currentState: versionData.state,
      version: versionData.version, // Include the actual version being used
      next: validTransitions.next,
      previous: validTransitions.previous,
      canTransitionNext,
      canTransitionPrevious
    });
  } catch (err) {
    console.error('Error getting valid transitions:', err);
    res.status(500).send('Server Error');
  }
});

// Get a specific requirement
router.get('/:id', auth, addGroupContext, resolveRequirementId, async (req, res) => {
  try {
    let query = Requirement.findById(req.resolvedId)
      .select('+type')  // Explicitly include the type field
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('approvers.user', 'username color firstName lastName avatar')
      .populate('approvals.user', 'username color firstName lastName avatar')
      .populate('versions.tasks.user', 'username color firstName lastName avatar')
      .populate('versions.comments.user', 'username color firstName lastName avatar')
      .populate('versions.createdBy', 'username color firstName lastName avatar')
      .populate('versions.releaseTags.addedBy', 'username firstName lastName');

    // Handle populate query parameter
    if (req.query.populate) {
      const populateFields = req.query.populate.split(',');
      populateFields.forEach(field => {
        if (field === 'project') {
          query = query.populate('project', 'name elementId createdBy');
        } else if (field === 'feature') {
          query = query.populate('feature', 'title elementId');
        }
      });
    }

    const requirement = await query;

    if (!requirement) {
      return res.status(404).json({ msg: 'Requirement not found' });
    }

    res.json(requirement);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Get children of a requirement or feature
router.get('/:id/children', auth, addGroupContext, async (req, res) => {
  try {
    // Resolve ElementID to ObjectID if needed (supports both Requirements and Features)
    let resolvedId = req.params.id;
    let userGroupId = null;

    // Get user group context
    if (req.user && req.user.group) {
      userGroupId = req.user.group;
    } else if (req.userGroup) {
      userGroupId = req.userGroup._id;
    }

    if (req.params.id && typeof req.params.id === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.id) || req.params.id.length !== 24) {
        // It's an ElementID, try to resolve it
        let doc = null;

        // First try as a requirement
        if (userGroupId && !req.isSuperUser) {
          doc = await Requirement.findOne({
            elementId: req.params.id,
            group: userGroupId
          });
        } else if (req.isSuperUser) {
          doc = await Requirement.findOne({ elementId: req.params.id });
        }

        // If not found as requirement, try as feature
        if (!doc) {
          if (userGroupId && !req.isSuperUser) {
            doc = await Feature.findOne({
              elementId: req.params.id,
              group: userGroupId
            });
          } else if (req.isSuperUser) {
            doc = await Feature.findOne({ elementId: req.params.id });
          }
        }

        if (!doc) {
          return res.status(404).json({ message: 'Parent not found' });
        }
        resolvedId = doc._id;
      }
    }

    // Build query with group filtering
    let childrenQuery = {
      $or: [
        { parent: resolvedId },
        { feature: resolvedId }
      ]
    };

    // Add group filtering unless super user
    if (userGroupId && !req.isSuperUser) {
      childrenQuery.group = userGroupId;
    }

    const children = await Requirement.find(childrenQuery)
      .populate('createdBy', 'username firstName lastName avatar')
      .populate('versions.createdBy', 'username firstName lastName avatar')
      .populate('versions.comments.user', 'username firstName lastName avatar')
      .populate('project', 'name elementId _id')
      .sort({ type: -1, createdAt: -1 });
    res.json(children);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update a requirement
router.put('/:id', auth, addGroupContext, resolveRequirementId, async (req, res) => {
  try {
    console.log('PUT /api/requirements/:id - Request body:', req.body);
    console.log('PUT /api/requirements/:id - User ID:', req.user.userId);

    const requirement = await Requirement.findById(req.resolvedId);

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    // Get the current version's data (but NOT comments - they should stay with their version)
    const currentVersion = requirement.versions[requirement.versions.length - 1];
    const currentLabels = currentVersion.labels || [];
    const currentTasks = currentVersion.tasks || [];

    console.log('Current version:', {
      title: currentVersion.title,
      description: currentVersion.description,
      state: currentVersion.state
    });

    // Track changes between versions (with size limits to prevent MongoDB document size issues)
    const changes = [];
    const MAX_CHANGE_TEXT_LENGTH = 1000; // Limit change text to prevent huge documents

    // Helper function to truncate large text
    const truncateText = (text) => {
      if (!text) return text;
      if (text.length <= MAX_CHANGE_TEXT_LENGTH) return text;
      return text.substring(0, MAX_CHANGE_TEXT_LENGTH) + '... [truncated due to size]';
    };

    // Helper function to strip HTML tags for change tracking
    const stripHtml = (html) => {
      if (!html) return html;
      // Remove HTML tags and decode HTML entities
      return html
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
        .replace(/&amp;/g, '&')  // Replace &amp; with &
        .replace(/&lt;/g, '<')   // Replace &lt; with <
        .replace(/&gt;/g, '>')   // Replace &gt; with >
        .replace(/&quot;/g, '"') // Replace &quot; with "
        .replace(/&#39;/g, "'")  // Replace &#39; with '
        .replace(/\s+/g, ' ')    // Replace multiple whitespace with single space
        .trim();
    };

    // Check title changes
    if (req.body.title !== currentVersion.title) {
      changes.push({
        field: 'title',
        from: truncateText(currentVersion.title),
        to: truncateText(req.body.title)
      });
    }

    // Check description changes
    if (req.body.description !== currentVersion.description) {
      changes.push({
        field: 'description',
        from: truncateText(stripHtml(currentVersion.description)),
        to: truncateText(stripHtml(req.body.description))
      });
    }

    // Check state changes
    if (req.body.state && req.body.state !== currentVersion.state) {
      changes.push({
        field: 'state',
        from: currentVersion.state,
        to: req.body.state
      });
    }

    const newVersion = {
      version: requirement.currentVersion + 1,
      title: req.body.title || currentVersion.title,
      description: req.body.description || currentVersion.description,
      state: req.body.state || 'New', // Reset to 'New' state for new versions unless explicitly specified
      labels: currentLabels,
      tasks: currentTasks,
      createdBy: req.user.userId,
      comments: [], // New version starts with empty comments
      changes: changes
    };

    console.log('New version to be created:', newVersion);

    // Check current document size first
    const currentDocSize = JSON.stringify(requirement.toObject()).length;
    const newVersionSize = JSON.stringify(newVersion).length;
    const estimatedSize = currentDocSize + newVersionSize;
    const MAX_DOCUMENT_SIZE = 15 * 1024 * 1024; // 15MB to leave some buffer

    console.log('Document size check:', {
      currentSize: (currentDocSize / 1024 / 1024).toFixed(2) + 'MB',
      newVersionSize: (newVersionSize / 1024).toFixed(1) + 'KB',
      estimatedTotal: (estimatedSize / 1024 / 1024).toFixed(2) + 'MB',
      limit: (MAX_DOCUMENT_SIZE / 1024 / 1024).toFixed(2) + 'MB'
    });

    if (currentDocSize > MAX_DOCUMENT_SIZE * 0.9) { // If already 90% of limit
      return res.status(400).json({
        message: 'Cannot add new version: Document already too large due to previous versions containing large images.',
        details: 'This requirement has accumulated too much data over multiple versions. Please create a new requirement or contact an administrator to clean up old versions.',
        currentSize: (currentDocSize / 1024 / 1024).toFixed(2) + 'MB',
        maxSize: (MAX_DOCUMENT_SIZE / 1024 / 1024).toFixed(2) + 'MB'
      });
    }

    if (estimatedSize > MAX_DOCUMENT_SIZE) {
      console.error('Document would exceed MongoDB size limit:', estimatedSize, 'bytes');
      return res.status(400).json({
        message: 'Document too large. Please reduce the size of images or content.',
        details: 'The requirement contains too much data (likely large images). Please use smaller images or external image storage.'
      });
    }

    requirement.versions.push(newVersion);
    requirement.currentVersion = requirement.versions.length;

    const updatedRequirement = await requirement.save();
    const populatedRequirement = await Requirement.findById(updatedRequirement._id)
      .populate('createdBy', 'username firstName lastName avatar')
      .populate('versions.createdBy', 'username firstName lastName avatar')
      .populate('versions.comments.user', 'username firstName lastName avatar');

    res.json(populatedRequirement);
  } catch (error) {
    console.error('Error updating requirement:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });

    // Send more detailed error information
    res.status(400).json({
      message: error.message,
      details: error.errors ? Object.keys(error.errors).map(key => ({
        field: key,
        message: error.errors[key].message
      })) : null
    });
  }
});

// Add a comment to a requirement
router.post('/:requirementId/comments', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    // Find the version that matches the requested version number
    const targetVersion = requirement.versions.find(v => v.version === req.body.version);
    if (!targetVersion) {
      return res.status(400).json({ message: 'Invalid version number' });
    }

    // Create the comment with the correct version
    const newComment = {
      text: req.body.text,
      user: req.user.userId,
      version: req.body.version,
      createdAt: new Date()
    };

    // Add the comment to the target version
    targetVersion.comments.push(newComment);

    const updatedRequirement = await requirement.save();
    const populatedRequirement = await Requirement.findById(updatedRequirement._id)
      .populate('createdBy', 'username firstName lastName avatar')
      .populate('versions.createdBy', 'username firstName lastName avatar')
      .populate('versions.comments.user', 'username firstName lastName avatar');

    // Return the newly created comment with populated user data
    const createdComment = populatedRequirement.versions
      .find(v => v.version === req.body.version)
      .comments[targetVersion.comments.length - 1];

    res.json(createdComment);
  } catch (error) {
    console.error('Error adding comment:', error);
    res.status(400).json({ message: error.message });
  }
});

// Get all comments for a requirement
router.get('/:requirementId/comments', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const requirement = await Requirement.findById(req.resolvedRequirementId)
      .populate('versions.comments.user', 'username firstName lastName avatar');

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    // Flatten all comments from all versions
    const allComments = requirement.versions.flatMap(version =>
      version.comments.map(comment => ({
        ...comment.toObject(),
        version: version.version
      }))
    ).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    res.json(allComments);
  } catch (error) {
    console.error('Error fetching comments:', error);
    res.status(500).json({ message: error.message });
  }
});

// Add a linked text to a requirement
router.post('/:requirementId/linked-texts', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    const { id, text, commentId, startOffset, endOffset, highlightColor, version } = req.body;

    // Create the linked text
    const newLinkedText = {
      id,
      text,
      commentId,
      startOffset,
      endOffset,
      highlightColor: highlightColor || '#1976d2',
      version: version || requirement.currentVersion,
      createdAt: new Date()
    };

    // Add the linked text to the requirement
    requirement.linkedTexts.push(newLinkedText);

    const updatedRequirement = await requirement.save();
    const populatedRequirement = await Requirement.findById(updatedRequirement._id)
      .populate('createdBy', 'username firstName lastName avatar')
      .populate('versions.createdBy', 'username firstName lastName avatar')
      .populate('versions.comments.user', 'username firstName lastName avatar');

    res.json(newLinkedText);
  } catch (error) {
    console.error('Error adding linked text:', error);
    res.status(500).json({ message: error.message });
  }
});

// Get all linked texts for a requirement
router.get('/:requirementId/linked-texts', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    // Filter by version if specified
    const version = req.query.version;
    let linkedTexts = requirement.linkedTexts || [];

    if (version) {
      linkedTexts = linkedTexts.filter(lt => lt.version === parseInt(version));
    }

    res.json(linkedTexts);
  } catch (error) {
    console.error('Error fetching linked texts:', error);
    res.status(500).json({ message: error.message });
  }
});

// Delete a linked text from a requirement
router.delete('/:requirementId/linked-texts/:linkId', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    // Remove the linked text
    requirement.linkedTexts = requirement.linkedTexts.filter(
      lt => lt.id !== req.params.linkId
    );

    await requirement.save();
    res.json({ message: 'Linked text removed successfully' });
  } catch (error) {
    console.error('Error removing linked text:', error);
    res.status(500).json({ message: error.message });
  }
});

// Add an approval to a requirement version
router.post('/:id/approve', auth, addGroupContext, resolveRequirementId, async (req, res) => {
  try {
    const userId = req.user.userId;
    const requirement = await Requirement.findById(req.resolvedId);

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    const currentVersion = requirement.versions[requirement.versions.length - 1];

    // Check if user has already approved this version
    const existingApproval = currentVersion.approvals.find(
      approval => approval.user.toString() === userId.toString()
    );

    if (existingApproval) {
      return res.status(400).json({ message: 'User has already approved this version' });
    }

    currentVersion.approvals.push({
      user: userId
    });

    await requirement.save();
    res.json(requirement);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Assign users to a requirement
router.post('/:id/assign', auth, addGroupContext, resolveRequirementId, async (req, res) => {
  try {
    const { userIds } = req.body;
    const requirement = await Requirement.findById(req.resolvedId);

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    requirement.assignedTo = userIds;
    await requirement.save();
    res.json(requirement);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Add a label to a requirement
router.post('/:id/labels', auth, addGroupContext, resolveRequirementId, async (req, res) => {
  try {
    const { labelId } = req.body;
    const requirement = await Requirement.findById(req.resolvedId);

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    const currentVersion = requirement.versions[requirement.versions.length - 1];
    if (!currentVersion.labels.includes(labelId)) {
      currentVersion.labels.push(labelId);
      await requirement.save();
    }

    const updatedRequirement = await Requirement.findById(req.resolvedId)
      .populate('createdBy', 'username firstName lastName avatar')
      .populate('versions.createdBy', 'username firstName lastName avatar')
      .populate('versions.comments.user', 'username firstName lastName avatar')
      .populate('versions.labels', 'name color');

    res.json(updatedRequirement);
  } catch (error) {
    console.error('Error adding label:', error);
    res.status(400).json({ message: error.message });
  }
});

// Remove a label from a requirement
router.delete('/:id/labels/:labelId', auth, addGroupContext, resolveRequirementId, async (req, res) => {
  try {
    const requirement = await Requirement.findById(req.resolvedId);

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    const currentVersion = requirement.versions[requirement.versions.length - 1];
    currentVersion.labels = currentVersion.labels.filter(
      label => label.toString() !== req.params.labelId
    );

    await requirement.save();

    const updatedRequirement = await Requirement.findById(req.resolvedId)
      .populate('createdBy', 'username firstName lastName avatar')
      .populate('versions.createdBy', 'username firstName lastName avatar')
      .populate('versions.comments.user', 'username firstName lastName avatar')
      .populate('versions.labels', 'name color');

    res.json(updatedRequirement);
  } catch (error) {
    console.error('Error removing label:', error);
    res.status(400).json({ message: error.message });
  }
});

// Add member to requirement
router.post('/:requirementId/members', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const { userId, roles, isApprover } = req.body;
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ msg: 'Requirement not found' });
    }

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Check if user is already a member
    const existingMemberIndex = requirement.members.findIndex(
      member => member.user.toString() === userId
    );

    if (existingMemberIndex >= 0) {
      // Add new roles to existing member
      const existingRoles = requirement.members[existingMemberIndex].roles;
      roles.forEach(role => {
        if (!existingRoles.includes(role)) {
          existingRoles.push(role);
        }
      });
    } else {
      // Add new member
      requirement.members.push({ user: userId, roles });
    }

    // Handle approver designation
    if (isApprover) {
      const existingApproverIndex = requirement.approvers.findIndex(
        approver => approver.user.toString() === userId
      );

      if (existingApproverIndex === -1) {
        // Add as approver if not already one
        requirement.approvers.push({
          user: userId,
          addedInVersion: requirement.currentVersion
        });
      }
    }

    await requirement.save();

    // Add user to feature members if not already a member (cascading up)
    if (requirement.feature) {
      const feature = await Feature.findById(requirement.feature);
      if (feature) {
        const isMember = feature.members.some(
          member => member.user.toString() === userId
        );

        if (!isMember) {
          feature.members.push({
            user: userId,
            roles: roles
          });
          await feature.save();
        }
      }
    }

    // Add user to project members if not already a member (cascading up)
    const project = await Project.findById(requirement.project);
    if (project) {
      const isMember = project.members.some(
        member => member.user.toString() === userId
      );

      if (!isMember) {
        project.members.push({
          user: userId,
          roles: roles
        });
        await project.save();
      }
    }

    // Return the requirement with populated user data
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('project', 'name createdBy')
      .populate('feature', 'title')
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('approvers.user', 'username color firstName lastName avatar')
      .populate('approvals.user', 'username color firstName lastName avatar')
      .populate('versions.tasks.user', 'username color firstName lastName avatar');

    res.json(populatedRequirement);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Remove member from requirement
router.delete('/:requirementId/members/:userId', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ msg: 'Requirement not found' });
    }

    requirement.members = requirement.members.filter(
      member => member.user.toString() !== req.params.userId
    );

    await requirement.save();

    // Return the requirement with populated user data
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('project', 'name createdBy')
      .populate('feature', 'title')
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('approvers.user', 'username color firstName lastName avatar')
      .populate('approvals.user', 'username color firstName lastName avatar')
      .populate('versions.tasks.user', 'username color firstName lastName avatar');

    res.json(populatedRequirement);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Update member roles
router.put('/:requirementId/members/:userId', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const { roles } = req.body;
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ msg: 'Requirement not found' });
    }

    const memberIndex = requirement.members.findIndex(
      member => member.user.toString() === req.params.userId
    );

    if (memberIndex === -1) {
      return res.status(404).json({ msg: 'Member not found' });
    }

    requirement.members[memberIndex].roles = roles;
    await requirement.save();

    // Return the requirement with populated user data
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('project', 'name createdBy')
      .populate('feature', 'title')
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('approvers.user', 'username color firstName lastName avatar')
      .populate('approvals.user', 'username color firstName lastName avatar')
      .populate('versions.tasks.user', 'username color firstName lastName avatar');

    res.json(populatedRequirement);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Add task to requirement
router.post('/:requirementId/tasks', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const { user, text, role } = req.body;
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ msg: 'Requirement not found' });
    }

    // Get current version
    const currentVersion = requirement.versions.find(
      v => v.version === requirement.currentVersion
    );

    if (!currentVersion) {
      return res.status(404).json({ msg: 'Current version not found' });
    }

    // Add task to current version
    const taskData = {
      user,
      text,
      status: 'pending'
    };

    // Only add role if one is provided
    if (role) {
      taskData.role = role;
    }

    currentVersion.tasks.push(taskData);

    // If user is already a member and has a role for this task, add the role if it's unique
    if (role) {
      const memberIndex = requirement.members.findIndex(
        member => member.user.toString() === user.toString()
      );

      if (memberIndex >= 0) {
        const existingRoles = requirement.members[memberIndex].roles;
        if (!existingRoles.includes(role)) {
          existingRoles.push(role);
        }
      }
    }

    await requirement.save();

    // Return the requirement with populated user data
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('project', 'name createdBy')
      .populate('feature', 'title')
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('approvers.user', 'username color firstName lastName avatar')
      .populate('approvals.user', 'username color firstName lastName avatar')
      .populate('versions.tasks.user', 'username color firstName lastName avatar');

    res.json(populatedRequirement);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Update task
router.put('/:requirementId/tasks/:taskId', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const { text, status, role } = req.body;
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ msg: 'Requirement not found' });
    }

    // Find the task across ALL versions, not just current version
    let foundTask = null;
    let foundVersion = null;

    for (const version of requirement.versions) {
      if (version.tasks) {
        const taskIndex = version.tasks.findIndex(
          task => task._id.toString() === req.params.taskId
        );
        if (taskIndex !== -1) {
          foundTask = version.tasks[taskIndex];
          foundVersion = version;
          break;
        }
      }
    }

    if (!foundTask) {
      return res.status(404).json({ msg: 'Task not found in any version' });
    }

    // Update task properties
    if (text !== undefined) foundTask.text = text;
    if (status !== undefined) foundTask.status = status;
    if (role !== undefined) {
      foundTask.role = role;

      // Update member roles
      const memberIndex = requirement.members.findIndex(
        member => member.user.toString() === foundTask.user.toString()
      );

      if (memberIndex >= 0) {
        const existingRoles = requirement.members[memberIndex].roles;
        if (!existingRoles.includes(role)) {
          existingRoles.push(role);
        }
      }
    }

    await requirement.save();

    // Return the requirement with populated user data
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('project', 'name createdBy')
      .populate('feature', 'title')
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('approvers.user', 'username color firstName lastName avatar')
      .populate('approvals.user', 'username color firstName lastName avatar')
      .populate('versions.tasks.user', 'username color firstName lastName avatar');

    res.json(populatedRequirement);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Toggle task completion status
router.put('/:requirementId/tasks/:taskId/toggle', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ msg: 'Requirement not found' });
    }

    // Find the task across ALL versions, not just current version
    let foundTask = null;
    let foundVersion = null;
    let taskIndex = -1;

    for (const version of requirement.versions) {
      if (version.tasks) {
        taskIndex = version.tasks.findIndex(
          task => task._id.toString() === req.params.taskId
        );
        if (taskIndex !== -1) {
          foundTask = version.tasks[taskIndex];
          foundVersion = version;
          break;
        }
      }
    }

    if (!foundTask) {
      return res.status(404).json({ msg: 'Task not found in any version' });
    }

    // Toggle between 'pending' and 'completed'
    foundTask.status = foundTask.status === 'pending' ? 'completed' : 'pending';

    await requirement.save();

    // Return the requirement with populated user data
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('project', 'name createdBy')
      .populate('feature', 'title')
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('approvers.user', 'username color firstName lastName avatar')
      .populate('approvals.user', 'username color firstName lastName avatar')
      .populate('versions.tasks.user', 'username color firstName lastName avatar');

    res.json(populatedRequirement);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Delete task
router.delete('/:requirementId/tasks/:taskId', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ msg: 'Requirement not found' });
    }

    // Find the task across ALL versions, not just current version
    let foundVersion = null;
    let taskFound = false;

    for (const version of requirement.versions) {
      if (version.tasks) {
        const taskIndex = version.tasks.findIndex(
          task => task._id.toString() === req.params.taskId
        );
        if (taskIndex !== -1) {
          // Remove the task from this version
          version.tasks.splice(taskIndex, 1);
          foundVersion = version;
          taskFound = true;
          break;
        }
      }
    }

    if (!taskFound) {
      return res.status(404).json({ msg: 'Task not found in any version' });
    }

    await requirement.save();

    // Return the requirement with populated user data
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('project', 'name createdBy')
      .populate('feature', 'title')
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('approvers.user', 'username color firstName lastName avatar')
      .populate('approvals.user', 'username color firstName lastName avatar')
      .populate('versions.tasks.user', 'username color firstName lastName avatar');

    res.json(populatedRequirement);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Toggle approval for a requirement
router.post('/:requirementId/approvals/toggle', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const requirement = await Requirement.findById(req.resolvedRequirementId)
      .populate('project', 'createdBy')
      .populate('approvers.user', '_id');

    if (!requirement) {
      return res.status(404).json({ msg: 'Requirement not found' });
    }

    const loggedInUserId = req.user.userId;
    const targetUserId = req.body.targetUserId || loggedInUserId; // Default to self if no target specified
    const currentVersion = requirement.currentVersion;

    // Check permissions first: project creator can approve for anyone
    const isProjectCreator = requirement.project.createdBy.toString() === loggedInUserId;

    if (!isProjectCreator) {
      // If not project creator, user must be an approver and can only approve for themselves
      const isTargetUserApprover = requirement.approvers.some(approver => {
        const approverId = typeof approver.user === 'object' ? approver.user._id : approver.user;
        return approverId.toString() === targetUserId;
      });

      const isSelfApproval = targetUserId === loggedInUserId;

      if (!isTargetUserApprover || !isSelfApproval) {
        return res.status(403).json({ msg: 'User can only approve for themselves if they are designated as an approver' });
      }
    }

    // Check if target user has already approved this version
    const existingApprovalIndex = requirement.approvals.findIndex(approval => {
      const approvalUserId = typeof approval.user === 'object' ? approval.user._id : approval.user;
      return approvalUserId.toString() === targetUserId && approval.version === currentVersion;
    });

    if (existingApprovalIndex >= 0) {
      // Remove approval
      requirement.approvals.splice(existingApprovalIndex, 1);
    } else {
      // Add approval
      requirement.approvals.push({
        user: targetUserId,
        version: currentVersion,
        approvedAt: new Date()
      });
    }

    await requirement.save();

    // Return the requirement with populated user data
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('project', 'name createdBy')
      .populate('feature', 'title')
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('approvers.user', 'username color firstName lastName avatar')
      .populate('approvals.user', 'username color firstName lastName avatar')
      .populate('versions.tasks.user', 'username color firstName lastName avatar');

    res.json(populatedRequirement);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Transition requirement state
router.post('/:requirementId/transition-state', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const { newState, version } = req.body;
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ msg: 'Requirement not found' });
    }

    const versionNumber = version || requirement.currentVersion;
    await requirement.transitionState(versionNumber, newState);

    // Return the requirement with populated user data
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('project', 'name createdBy')
      .populate('feature', 'title')
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('approvers.user', 'username color firstName lastName avatar')
      .populate('approvals.user', 'username color firstName lastName avatar')
      .populate('versions.tasks.user', 'username color firstName lastName avatar');

    res.json(populatedRequirement);
  } catch (err) {
    console.error('Error transitioning requirement state:', err);
    res.status(400).json({ msg: err.message });
  }
});

// Get test suite for a specific requirement version
router.get('/:requirementId/tests/:version', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    const version = parseInt(req.params.version);
    let testSuite = requirement.testSuites.find(ts => ts.version === version);

    // If no test suite exists for this version, create a default one with 3 empty rows
    if (!testSuite) {
      testSuite = {
        version: version,
        tests: Array.from({ length: 3 }, (_, index) => ({
          id: `test_${version}_${index + 1}`,
          testName: '',
          setup: '',
          steps: '',
          expectedResult: '',
          results: [null, null] // Two result columns
        })),
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }

    res.json(testSuite);
  } catch (error) {
    console.error('Error fetching test suite:', error);
    res.status(500).json({ message: 'Server Error' });
  }
});

// Update test suite for a specific requirement version
router.put('/:requirementId/tests/:version', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const requirement = await Requirement.findById(req.resolvedRequirementId);

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    const version = parseInt(req.params.version);
    const { tests } = req.body;

    // Clean the test data to ensure proper format
    const cleanedTests = tests.map(test => ({
      ...test,
      results: (test.results || []).map(result => {
        if (!result || (typeof result === 'object' && !result.status && !result.date)) {
          return null;
        }
        return {
          date: result.date || null,
          status: result.status || null,
          bugNumbers: result.bugNumbers || [],
          notes: result.notes || ''
        };
      }).filter(result => result !== null) // Remove completely null results
    }));

    // Find existing test suite or create new one
    let testSuiteIndex = requirement.testSuites.findIndex(ts => ts.version === version);

    if (testSuiteIndex === -1) {
      // Create new test suite
      requirement.testSuites.push({
        version: version,
        tests: cleanedTests,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } else {
      // Update existing test suite
      requirement.testSuites[testSuiteIndex].tests = cleanedTests;
      requirement.testSuites[testSuiteIndex].updatedAt = new Date();
    }

    await requirement.save();

    const updatedTestSuite = requirement.testSuites.find(ts => ts.version === version);
    res.json(updatedTestSuite);
  } catch (error) {
    console.error('Error updating test suite:', error);
    res.status(500).json({ message: 'Server Error' });
  }
});

// Add release tag to requirement (current version)
router.post('/:requirementId/tags', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const { tag } = req.body;

    if (!tag || !tag.trim()) {
      return res.status(400).json({ message: 'Tag is required' });
    }

    const requirement = await Requirement.findById(req.resolvedRequirementId);
    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && requirement.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Add tag to current version
    await requirement.addReleaseTag(tag.trim(), req.user.userId);

    // Return the requirement with populated user data
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('project', 'name createdBy')
      .populate('feature', 'title')
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.releaseTags.addedBy', 'username firstName lastName');

    res.json(populatedRequirement);
  } catch (err) {
    console.error('Error adding release tag to requirement:', err);
    res.status(500).send('Server Error');
  }
});

// Add release tag to specific requirement version
router.post('/:requirementId/versions/:versionNumber/tags', auth, addGroupContext, async (req, res) => {
  try {
    const { tag } = req.body;
    const versionNumber = parseInt(req.params.versionNumber);

    if (!tag || !tag.trim()) {
      return res.status(400).json({ message: 'Tag is required' });
    }

    if (isNaN(versionNumber) || versionNumber < 1) {
      return res.status(400).json({ message: 'Invalid version number' });
    }

    const requirement = await Requirement.findById(req.resolvedRequirementId);
    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && requirement.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Add tag to specific version
    await requirement.addReleaseTag(tag.trim(), req.user.userId, versionNumber);

    // Return the requirement with populated user data
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.releaseTags.addedBy', 'username firstName lastName');

    res.json(populatedRequirement);
  } catch (err) {
    console.error('Error adding release tag to requirement version:', err);
    if (err.message.includes('Version') && err.message.includes('not found')) {
      return res.status(404).json({ message: err.message });
    }
    res.status(500).send('Server Error');
  }
});

// Remove release tag from requirement (current version)
router.delete('/:requirementId/tags/:tag', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    const requirement = await Requirement.findById(req.resolvedRequirementId);
    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && requirement.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Remove tag from current version
    await requirement.removeReleaseTag(req.params.tag);

    // Return the requirement with populated user data
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('project', 'name createdBy')
      .populate('feature', 'title')
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.releaseTags.addedBy', 'username firstName lastName');

    res.json(populatedRequirement);
  } catch (err) {
    console.error('Error removing release tag from requirement:', err);
    res.status(500).send('Server Error');
  }
});

// Remove release tag from specific requirement version
router.delete('/:requirementId/versions/:versionNumber/tags/:tag', auth, addGroupContext, async (req, res) => {
  try {
    const versionNumber = parseInt(req.params.versionNumber);

    if (isNaN(versionNumber) || versionNumber < 1) {
      return res.status(400).json({ message: 'Invalid version number' });
    }

    const requirement = await Requirement.findById(req.resolvedRequirementId);
    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && requirement.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Remove tag from specific version
    await requirement.removeReleaseTag(req.params.tag, versionNumber);

    // Return the requirement with populated user data
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('project', 'name createdBy')
      .populate('feature', 'title')
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.releaseTags.addedBy', 'username firstName lastName');

    res.json(populatedRequirement);
  } catch (err) {
    console.error('Error removing release tag from requirement version:', err);
    if (err.message.includes('Version') && err.message.includes('not found')) {
      return res.status(404).json({ message: err.message });
    }
    res.status(500).send('Server Error');
  }
});

// Remove release tag from specific requirement version
router.delete('/:requirementId/versions/:versionNumber/tags/:tag', auth, addGroupContext, async (req, res) => {
  try {
    const versionNumber = parseInt(req.params.versionNumber);

    if (isNaN(versionNumber) || versionNumber < 1) {
      return res.status(400).json({ message: 'Invalid version number' });
    }

    const requirement = await Requirement.findById(req.resolvedRequirementId);
    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && requirement.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Remove tag from specific version
    await requirement.removeReleaseTag(req.params.tag, versionNumber);

    // Return the requirement with populated user data
    const populatedRequirement = await Requirement.findById(requirement._id)
      .populate('project', 'name createdBy')
      .populate('feature', 'title')
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.releaseTags.addedBy', 'username firstName lastName');

    res.json(populatedRequirement);
  } catch (err) {
    console.error('Error removing release tag from requirement version:', err);
    if (err.message.includes('Version') && err.message.includes('not found')) {
      return res.status(404).json({ message: err.message });
    }
    res.status(500).send('Server Error');
  }
});

module.exports = router;
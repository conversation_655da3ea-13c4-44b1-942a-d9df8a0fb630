const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const cacheMetrics = require('../utils/cacheMetrics');

/**
 * Cache Management Routes
 * These routes are for monitoring and managing the ElementID cache system
 */

// Get cache performance metrics
router.get('/metrics', auth, async (req, res) => {
  try {
    // Only allow super users to view cache metrics
    if (!req.isSuperUser) {
      return res.status(403).json({ message: 'Access denied. Super user required.' });
    }

    const report = cacheMetrics.getPerformanceReport();
    res.json({
      success: true,
      data: report,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Cache metrics error:', error);
    res.status(500).json({ message: 'Failed to retrieve cache metrics' });
  }
});

// Clear all caches (Redis + in-memory)
router.post('/clear', auth, async (req, res) => {
  try {
    // Only allow super users to clear cache
    if (!req.isSuperUser) {
      return res.status(403).json({ message: 'Access denied. Super user required.' });
    }

    // Clear Redis cache if available
    const redis = require('redis');
    let redisCleared = false;
    try {
      if (process.env.REDIS_URL || process.env.NODE_ENV === 'production') {
        const redisClient = redis.createClient(process.env.REDIS_URL);
        await redisClient.flushDb();
        await redisClient.quit();
        redisCleared = true;
      }
    } catch (redisError) {
      console.warn('Redis cache clear failed:', redisError.message);
    }

    // Clear in-memory caches (this is handled in the middleware file)
    // We'll need to expose a clear function from the middleware

    // Reset metrics
    cacheMetrics.reset();

    res.json({
      success: true,
      message: 'Cache cleared successfully',
      details: {
        redisCleared,
        memoryCleared: true,
        metricsReset: true
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Cache clear error:', error);
    res.status(500).json({ message: 'Failed to clear cache' });
  }
});

// Get cache statistics summary
router.get('/stats', auth, async (req, res) => {
  try {
    // Only allow super users to view cache stats
    if (!req.isSuperUser) {
      return res.status(403).json({ message: 'Access denied. Super user required.' });
    }

    const report = cacheMetrics.getPerformanceReport();
    
    // Simplified stats for dashboard display
    const stats = {
      overall: {
        cacheEfficiency: report.performance.cacheEfficiency,
        timeSaved: report.performance.estimatedTimeSaved,
        uptime: report.uptime
      },
      objectIdCache: {
        hitRate: report.objectIdCache.hitRate,
        totalRequests: report.objectIdCache.totalRequests,
        avgResponseTime: report.objectIdCache.avgResponseTime
      },
      elementIdCache: {
        hitRate: report.elementIdCache.hitRate,
        totalRequests: report.elementIdCache.totalRequests,
        avgResponseTime: report.elementIdCache.avgResponseTime
      },
      database: {
        totalQueries: report.databaseQueries.objectIdQueries + report.databaseQueries.elementIdQueries,
        avgQueryTime: report.databaseQueries.avgQueryTime
      }
    };

    res.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Cache stats error:', error);
    res.status(500).json({ message: 'Failed to retrieve cache statistics' });
  }
});

// Health check for cache system
router.get('/health', auth, async (req, res) => {
  try {
    // Only allow super users to check cache health
    if (!req.isSuperUser) {
      return res.status(403).json({ message: 'Access denied. Super user required.' });
    }

    const health = {
      redis: {
        available: false,
        connected: false,
        error: null
      },
      memory: {
        available: true,
        objectIdCacheSize: 0,
        elementIdCacheSize: 0
      },
      metrics: {
        collecting: true,
        totalRequests: 0
      }
    };

    // Check Redis health
    try {
      const redis = require('redis');
      if (process.env.REDIS_URL || process.env.NODE_ENV === 'production') {
        const redisClient = redis.createClient(process.env.REDIS_URL);
        await redisClient.ping();
        health.redis.available = true;
        health.redis.connected = true;
        await redisClient.quit();
      }
    } catch (redisError) {
      health.redis.error = redisError.message;
    }

    // Get metrics info
    const report = cacheMetrics.getPerformanceReport();
    health.metrics.totalRequests = report.objectIdCache.totalRequests + report.elementIdCache.totalRequests;

    const status = health.redis.available || health.memory.available ? 'healthy' : 'degraded';

    res.json({
      success: true,
      status,
      data: health,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Cache health check error:', error);
    res.status(500).json({ 
      success: false,
      status: 'unhealthy',
      message: 'Cache health check failed',
      error: error.message
    });
  }
});

module.exports = router;

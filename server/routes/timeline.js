const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const { addGroupContext } = require('../middleware/groupAuth');
const { resolveRequirementIdParam } = require('../middleware/elementIdResolver');
const TimelineService = require('../services/TimelineService');
const ArchiveService = require('../services/ArchiveService');

// Get timeline for a specific requirement
router.get('/requirement/:requirementId', auth, addGroupContext, async (req, res) => {
  try {
    const {
      includeArchived = false,
      version = null,
      eventTypes = null,
      limit = null,
      offset = 0
    } = req.query;

    const options = {
      includeArchived: includeArchived === 'true',
      version: version ? parseInt(version) : null,
      eventTypes: eventTypes ? eventTypes.split(',') : null,
      limit: limit ? parseInt(limit) : null,
      offset: parseInt(offset) || 0
    };

    // Use the requirementId directly (can be ObjectID or ElementID)
    const timeline = await TimelineService.getRequirementTimeline(
      req.params.requirementId,
      req.userGroup._id,
      options
    );

    res.json(timeline);
  } catch (error) {
    console.error('Error getting requirement timeline:', error);
    res.status(500).json({ message: error.message });
  }
});

// Get requirement with timeline (combined endpoint)
router.get('/requirement/:requirementId/with-requirement', auth, addGroupContext, async (req, res) => {
  try {
    const timelineOptions = {
      includeArchived: req.query.includeArchived === 'true',
      version: req.query.version ? parseInt(req.query.version) : null,
      eventTypes: req.query.eventTypes ? req.query.eventTypes.split(',') : null,
      limit: req.query.limit ? parseInt(req.query.limit) : null,
      offset: parseInt(req.query.offset) || 0
    };

    const result = await TimelineService.getRequirementWithTimeline(
      req.params.requirementId,
      req.userGroup._id,
      timelineOptions
    );

    res.json(result);
  } catch (error) {
    console.error('Error getting requirement with timeline:', error);
    res.status(500).json({ message: error.message });
  }
});

// Get user activity timeline
router.get('/user/:userId/activity', auth, addGroupContext, async (req, res) => {
  try {
    // Verify user has access to view this user's activity
    if (!req.user.isSuperUser && req.user.userId !== req.params.userId) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const options = {
      limit: parseInt(req.query.limit) || 50,
      offset: parseInt(req.query.offset) || 0,
      startDate: req.query.startDate || null,
      endDate: req.query.endDate || null
    };

    const activity = await TimelineService.getUserActivity(
      req.params.userId,
      req.userGroup._id,
      options
    );

    res.json(activity);
  } catch (error) {
    console.error('Error getting user activity:', error);
    res.status(500).json({ message: error.message });
  }
});

// Get project timeline
router.get('/project/:projectId', auth, addGroupContext, async (req, res) => {
  try {
    const options = {
      limit: parseInt(req.query.limit) || 100,
      offset: parseInt(req.query.offset) || 0
    };

    const timeline = await TimelineService.getProjectTimeline(
      req.params.projectId,
      req.userGroup._id,
      options
    );

    res.json(timeline);
  } catch (error) {
    console.error('Error getting project timeline:', error);
    res.status(500).json({ message: error.message });
  }
});

// Get timeline statistics for a requirement
router.get('/requirement/:requirementId/stats', auth, addGroupContext, async (req, res) => {
  try {
    const stats = await TimelineService.getTimelineStats(
      req.params.requirementId,
      req.userGroup._id
    );

    res.json(stats);
  } catch (error) {
    console.error('Error getting timeline stats:', error);
    res.status(500).json({ message: error.message });
  }
});

// Admin routes for archival management
router.get('/admin/archival-stats', auth, addGroupContext, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isSuperUser && !req.user.isGroupAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const stats = await ArchiveService.getArchivalStats();
    res.json(stats);
  } catch (error) {
    console.error('Error getting archival stats:', error);
    res.status(500).json({ message: error.message });
  }
});

// Check retention warnings for current group
router.get('/admin/retention-warnings', auth, addGroupContext, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isSuperUser && !req.user.isGroupAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const warningCheck = req.userGroup.needsRetentionWarning();
    const recommendations = await ArchiveService.getRetentionRecommendations(req.userGroup._id);

    res.json({
      warning: warningCheck,
      recommendations,
      currentPolicy: req.userGroup.getRetentionPolicy()
    });
  } catch (error) {
    console.error('Error checking retention warnings:', error);
    res.status(500).json({ message: error.message });
  }
});

// Mock purchase more time
router.post('/admin/purchase-extension', auth, addGroupContext, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isSuperUser && !req.user.isGroupAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const { extensionYears } = req.body;
    
    if (!extensionYears || ![1, 2, 3].includes(extensionYears)) {
      return res.status(400).json({ message: 'Invalid extension period. Must be 1, 2, or 3 years.' });
    }

    const purchaseDetails = await ArchiveService.mockPurchaseMoreTime(
      req.userGroup._id,
      extensionYears
    );

    res.json(purchaseDetails);
  } catch (error) {
    console.error('Error creating purchase extension:', error);
    res.status(500).json({ message: error.message });
  }
});

// Manually trigger archival for current group (admin only)
router.post('/admin/trigger-archival', auth, addGroupContext, async (req, res) => {
  try {
    // Check if user is super admin
    if (!req.user.isSuperUser) {
      return res.status(403).json({ message: 'Super admin access required' });
    }

    const result = await TimelineService.archiveOldEvents(req.userGroup._id);
    res.json(result);
  } catch (error) {
    console.error('Error triggering archival:', error);
    res.status(500).json({ message: error.message });
  }
});

// Restore archived events (emergency recovery)
router.post('/admin/restore-archived/:requirementId', auth, addGroupContext, resolveRequirementIdParam, async (req, res) => {
  try {
    // Check if user is super admin
    if (!req.user.isSuperUser) {
      return res.status(403).json({ message: 'Super admin access required' });
    }

    const options = {
      startDate: req.body.startDate || null,
      endDate: req.body.endDate || null,
      eventTypes: req.body.eventTypes || null
    };

    const result = await ArchiveService.restoreArchivedEvents(
      req.resolvedRequirementId,
      req.userGroup._id,
      options
    );

    res.json(result);
  } catch (error) {
    console.error('Error restoring archived events:', error);
    res.status(500).json({ message: error.message });
  }
});

// Get valid event types (for documentation/validation)
router.get('/event-types', auth, (req, res) => {
  try {
    const TimelineEvent = require('../models/TimelineEvent');
    const validTypes = TimelineEvent.getValidEventTypes();
    
    res.json({
      eventTypes: validTypes,
      description: 'Valid event types for timeline events'
    });
  } catch (error) {
    console.error('Error getting event types:', error);
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;

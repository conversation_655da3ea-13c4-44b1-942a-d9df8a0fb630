const mongoose = require('mongoose');

const timelineEventSchema = new mongoose.Schema({
  requirementId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Requirement',
    required: true,
    index: true
  },
  groupId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Group',
    required: true,
    index: true // Critical for multi-tenancy
  },
  eventType: {
    type: String,
    required: true,
    index: true,
    validate: {
      validator: function(v) {
        // Allow any event type that follows our naming convention
        return /^[a-z_]+$/.test(v);
      },
      message: 'Event type must be lowercase with underscores'
    }
  },
  timestamp: {
    type: Date,
    default: Date.now,
    required: true,
    index: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  version: {
    type: Number,
    required: false // Some events may not be version-specific
  },
  // Event-specific data stored as flexible object
  eventData: {
    // For state_changed: { from: 'New', to: 'Being Drafted' }
    // For tag_added: { tag: 'v1.0.0' }
    // For comment_added: { commentId: ObjectId, text: 'preview...' }
    // For approval_given: { version: 1 }
    type: mongoose.Schema.Types.Mixed,
    required: false
  },
  // Human-readable description for UI display
  description: {
    type: String,
    required: true
  },
  // Archive tracking
  archived: {
    type: Boolean,
    default: false,
    index: true
  },
  archivedAt: {
    type: Date,
    required: false
  }
}, {
  timestamps: true
});

// Multi-tenancy and performance indexes
timelineEventSchema.index({ groupId: 1, requirementId: 1, timestamp: -1 });
timelineEventSchema.index({ groupId: 1, eventType: 1, timestamp: -1 });
timelineEventSchema.index({ groupId: 1, user: 1, timestamp: -1 });
timelineEventSchema.index({ groupId: 1, archived: 1, timestamp: -1 });
timelineEventSchema.index({ requirementId: 1, version: 1, timestamp: -1 });

// Compound index for timeline queries
timelineEventSchema.index({ 
  groupId: 1, 
  requirementId: 1, 
  archived: 1, 
  timestamp: -1 
});

// Static method to get valid event types (for validation/documentation)
timelineEventSchema.statics.getValidEventTypes = function() {
  return [
    'created',           // Requirement created
    'version_created',   // New version created
    'state_changed',     // State transition
    'tag_added',         // Release tag added
    'tag_removed',       // Release tag removed
    'comment_added',     // Comment added
    'approval_given',    // Approval granted
    'approval_removed',  // Approval revoked
    'member_added',      // Member assigned
    'member_removed',    // Member unassigned
    'task_added',        // Task created
    'task_completed',    // Task marked complete
    'task_updated',      // Task modified
    'test_result',       // Test result recorded
    'label_added',       // Label attached
    'label_removed',     // Label removed
    'document_generated', // Document generation
    // Future event types can be added without schema changes
    'bug_reported',      // Bug tracking
    'bug_fixed',
    'corrective_action_created',
    'corrective_action_completed'
  ];
};

// Method to validate event type
timelineEventSchema.statics.isValidEventType = function(eventType) {
  return /^[a-z_]+$/.test(eventType);
};

// Method to create standardized descriptions
timelineEventSchema.statics.createDescription = function(eventType, eventData, user) {
  const descriptions = {
    created: 'Requirement created',
    version_created: `Version ${eventData.version} created`,
    state_changed: `State changed from "${eventData.from}" to "${eventData.to}"`,
    tag_added: `Added release tag "${eventData.tag}"`,
    tag_removed: `Removed release tag "${eventData.tag}"`,
    comment_added: `Added comment: "${eventData.text?.substring(0, 50)}${eventData.text?.length > 50 ? '...' : ''}"`,
    approval_given: `Approved version ${eventData.version}`,
    approval_removed: `Removed approval for version ${eventData.version}`,
    member_added: `Added member with roles: ${eventData.roles?.join(', ')}`,
    member_removed: `Removed member`,
    task_added: `Added task: "${eventData.text}"`,
    task_completed: `Completed task: "${eventData.text}"`,
    task_updated: `Updated task: "${eventData.text}"`,
    test_result: `Test result: ${eventData.result} for "${eventData.testName}"`,
    label_added: `Added label: "${eventData.labelName}"`,
    label_removed: `Removed label: "${eventData.labelName}"`,
    document_generated: `Generated ${eventData.documentType}: "${eventData.title}"`
  };

  return descriptions[eventType] || `${eventType.replace(/_/g, ' ')} event`;
};

const TimelineEvent = mongoose.model('TimelineEvent', timelineEventSchema);

module.exports = TimelineEvent;

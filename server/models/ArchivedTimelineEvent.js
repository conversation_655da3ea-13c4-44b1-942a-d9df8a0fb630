const mongoose = require('mongoose');

const archivedTimelineEventSchema = new mongoose.Schema({
  originalEventId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    index: true
  },
  originalRequirementId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    index: true
  },
  groupId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Group',
    required: true,
    index: true
  },
  archivedAt: {
    type: Date,
    default: Date.now,
    required: true,
    index: true
  },
  retentionTier: {
    type: String,
    enum: ['small', 'medium', 'large', 'enterprise'],
    required: true
  },
  // Compressed original event data
  eventData: {
    eventType: String,
    timestamp: Date,
    user: mongoose.Schema.Types.ObjectId,
    version: Number,
    eventData: mongoose.Schema.Types.Mixed,
    description: String,
    originalCreatedAt: Date,
    originalUpdatedAt: Date
  }
}, {
  timestamps: true
});

// Indexes for archive queries
archivedTimelineEventSchema.index({ groupId: 1, archivedAt: -1 });
archivedTimelineEventSchema.index({ originalRequirementId: 1, archivedAt: -1 });
archivedTimelineEventSchema.index({ retentionTier: 1, archivedAt: -1 });

// Method to restore archived event
archivedTimelineEventSchema.methods.restore = async function() {
  const TimelineEvent = require('./TimelineEvent');
  
  const restoredEvent = new TimelineEvent({
    requirementId: this.originalRequirementId,
    groupId: this.groupId,
    eventType: this.eventData.eventType,
    timestamp: this.eventData.timestamp,
    user: this.eventData.user,
    version: this.eventData.version,
    eventData: this.eventData.eventData,
    description: this.eventData.description,
    archived: false
  });

  await restoredEvent.save();
  await this.deleteOne();
  
  return restoredEvent;
};

const ArchivedTimelineEvent = mongoose.model('ArchivedTimelineEvent', archivedTimelineEventSchema);

module.exports = ArchivedTimelineEvent;

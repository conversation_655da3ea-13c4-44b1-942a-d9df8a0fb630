const mongoose = require('mongoose');

const groupSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  code: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  tier: {
    type: String,
    enum: ['1-3', '4-25', '26-100', '100+'],
    required: true
  },
  maxUsers: {
    type: Number,
    required: true
  },
  welcomeMessage: {
    type: String,
    default: 'Welcome to our team! We\'re excited to have you on board.'
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  // Timeline data retention policy
  retentionPolicy: {
    tier: {
      type: String,
      enum: ['small', 'medium', 'large', 'enterprise'],
      default: function() {
        // Auto-determine tier based on maxUsers
        if (this.maxUsers <= 3) return 'small';
        if (this.maxUsers <= 25) return 'medium';
        if (this.maxUsers <= 100) return 'large';
        return 'enterprise';
      }
    },
    retentionMonths: {
      type: Number,
      default: function() {
        // Auto-determine retention based on tier
        const tierMonths = {
          small: 12,      // 1 year
          medium: 24,     // 2 years
          large: 36,      // 3 years
          enterprise: 60  // 5 years
        };
        return tierMonths[this.retentionPolicy?.tier] || 12;
      }
    },
    expirationWarningDays: {
      type: [Number],
      default: [30, 14, 7, 1] // Warn at 30, 14, 7, and 1 day before expiration
    },
    lastWarningDate: Date,
    lastArchivalDate: Date,
    purchaseMoreTimeUrl: {
      type: String,
      default: '/admin/extend-retention' // Mock URL for now
    },
    archivalEnabled: {
      type: Boolean,
      default: false // Disabled by default, configurable for production
    },
    archivalFrequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly'],
      default: 'monthly'
    }
  }
}, {
  timestamps: true
});

// Helper function to get max users based on tier
groupSchema.statics.getMaxUsersForTier = function(tier) {
  const tierLimits = {
    '1-3': 3,
    '4-25': 25,
    '26-100': 100,
    '100+': 1000 // Set a reasonable upper limit
  };
  return tierLimits[tier] || 3;
};

// Pre-save middleware to set maxUsers based on tier
groupSchema.pre('save', function(next) {
  if (this.isModified('tier') || this.isNew) {
    this.maxUsers = this.constructor.getMaxUsersForTier(this.tier);
  }
  this.updatedAt = Date.now();
  next();
});

// Method to generate group code from name
groupSchema.statics.generateCodeFromName = function(name) {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
};

// Method to check if code is available
groupSchema.statics.isCodeAvailable = async function(code) {
  const existingGroup = await this.findOne({ code });
  return !existingGroup;
};

// Method to get retention policy details
groupSchema.methods.getRetentionPolicy = function() {
  const tierPolicies = {
    small: { maxUsers: 3, retentionMonths: 12 },
    medium: { maxUsers: 25, retentionMonths: 24 },
    large: { maxUsers: 100, retentionMonths: 36 },
    enterprise: { maxUsers: Infinity, retentionMonths: 60 }
  };

  return tierPolicies[this.retentionPolicy.tier] || tierPolicies.small;
};

// Method to check if timeline data needs archival warning
groupSchema.methods.needsRetentionWarning = function() {
  const policy = this.getRetentionPolicy();
  const cutoffDate = new Date();
  cutoffDate.setMonth(cutoffDate.getMonth() - policy.retentionMonths);

  // Check if we're within warning period
  const now = new Date();
  const warningDates = this.retentionPolicy.expirationWarningDays.map(days => {
    const warningDate = new Date(cutoffDate);
    warningDate.setDate(warningDate.getDate() + days);
    return warningDate;
  });

  // Find the next warning date that hasn't been sent
  const nextWarning = warningDates.find(date =>
    date <= now &&
    (!this.retentionPolicy.lastWarningDate || date > this.retentionPolicy.lastWarningDate)
  );

  return {
    needsWarning: !!nextWarning,
    warningDate: nextWarning,
    daysUntilExpiration: Math.ceil((cutoffDate - now) / (1000 * 60 * 60 * 24))
  };
};

// Method to calculate timeline data expiration date
groupSchema.methods.getTimelineExpirationDate = function() {
  const policy = this.getRetentionPolicy();
  const expirationDate = new Date();
  expirationDate.setMonth(expirationDate.getMonth() - policy.retentionMonths);
  return expirationDate;
};

module.exports = mongoose.model('Group', groupSchema);

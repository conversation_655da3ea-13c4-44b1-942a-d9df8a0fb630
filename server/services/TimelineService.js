const mongoose = require('mongoose');
const TimelineEvent = require('../models/TimelineEvent');
const ArchivedTimelineEvent = require('../models/ArchivedTimelineEvent');
const Requirement = require('../models/Requirement');

class TimelineService {
  /**
   * Add a timeline event with proper multi-tenancy and validation
   */
  static async addEvent(requirementId, eventType, userId, groupId, eventData = {}, version = null) {
    try {
      // Validate inputs
      if (!mongoose.Types.ObjectId.isValid(requirementId)) {
        throw new Error('Invalid requirement ID');
      }
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('Invalid user ID');
      }
      if (!mongoose.Types.ObjectId.isValid(groupId)) {
        throw new Error('Invalid group ID');
      }
      if (!TimelineEvent.isValidEventType(eventType)) {
        throw new Error(`Invalid event type: ${eventType}`);
      }

      // Verify requirement exists and belongs to the group
      const requirement = await Requirement.findOne({
        _id: requirementId,
        group: groupId
      });
      
      if (!requirement) {
        throw new Error('Requirement not found or access denied');
      }

      // Create description
      const description = TimelineEvent.createDescription(eventType, eventData, userId);

      // Create timeline event
      const timelineEvent = new TimelineEvent({
        requirementId,
        groupId,
        eventType,
        user: userId,
        version,
        eventData,
        description,
        timestamp: new Date()
      });

      await timelineEvent.save();
      return timelineEvent;
    } catch (error) {
      console.error('Error adding timeline event:', error);
      throw error;
    }
  }

  /**
   * Get timeline for a specific requirement with multi-tenancy
   */
  static async getRequirementTimeline(requirementId, groupId, options = {}) {
    try {
      const {
        includeArchived = false,
        version = null,
        eventTypes = null,
        limit = null,
        offset = 0
      } = options;

      // Build query with multi-tenancy
      const query = {
        requirementId: mongoose.Types.ObjectId(requirementId),
        groupId: mongoose.Types.ObjectId(groupId)
      };

      if (!includeArchived) {
        query.archived = false;
      }

      if (version !== null) {
        query.$or = [
          { version: version },
          { version: null } // Include version-agnostic events
        ];
      }

      if (eventTypes && Array.isArray(eventTypes)) {
        query.eventType = { $in: eventTypes };
      }

      let timelineQuery = TimelineEvent.find(query)
        .populate('user', 'username firstName lastName avatar color')
        .sort({ timestamp: 1 });

      if (limit) {
        timelineQuery = timelineQuery.limit(limit).skip(offset);
      }

      const timeline = await timelineQuery.exec();
      return timeline;
    } catch (error) {
      console.error('Error getting requirement timeline:', error);
      throw error;
    }
  }

  /**
   * Get requirement with its timeline (service layer abstraction)
   */
  static async getRequirementWithTimeline(requirementId, groupId, timelineOptions = {}) {
    try {
      // Get requirement and timeline in parallel
      const [requirement, timeline] = await Promise.all([
        Requirement.findOne({
          _id: requirementId,
          group: groupId
        })
        .populate('createdBy', 'username color firstName lastName avatar')
        .populate('members.user', 'username color firstName lastName avatar')
        .populate('approvers.user', 'username color firstName lastName avatar')
        .populate('approvals.user', 'username color firstName lastName avatar')
        .populate('versions.tasks.user', 'username color firstName lastName avatar')
        .populate('versions.comments.user', 'username color firstName lastName avatar')
        .populate('versions.createdBy', 'username color firstName lastName avatar')
        .populate('feature', 'title elementId')
        .populate('project', 'name elementId'),
        
        this.getRequirementTimeline(requirementId, groupId, timelineOptions)
      ]);

      if (!requirement) {
        throw new Error('Requirement not found or access denied');
      }

      return {
        requirement,
        timeline
      };
    } catch (error) {
      console.error('Error getting requirement with timeline:', error);
      throw error;
    }
  }

  /**
   * Get user activity timeline across requirements
   */
  static async getUserActivity(userId, groupId, options = {}) {
    try {
      const {
        limit = 50,
        offset = 0,
        startDate = null,
        endDate = null
      } = options;

      const query = {
        user: mongoose.Types.ObjectId(userId),
        groupId: mongoose.Types.ObjectId(groupId),
        archived: false
      };

      if (startDate || endDate) {
        query.timestamp = {};
        if (startDate) query.timestamp.$gte = new Date(startDate);
        if (endDate) query.timestamp.$lte = new Date(endDate);
      }

      const activity = await TimelineEvent.find(query)
        .populate('user', 'username firstName lastName avatar color')
        .populate('requirementId', 'elementId versions.title')
        .sort({ timestamp: -1 })
        .limit(limit)
        .skip(offset);

      return activity;
    } catch (error) {
      console.error('Error getting user activity:', error);
      throw error;
    }
  }

  /**
   * Get project-wide timeline
   */
  static async getProjectTimeline(projectId, groupId, options = {}) {
    try {
      const { limit = 100, offset = 0 } = options;

      // First get all requirements for the project
      const requirements = await Requirement.find({
        project: projectId,
        group: groupId
      }).select('_id');

      const requirementIds = requirements.map(req => req._id);

      const timeline = await TimelineEvent.find({
        requirementId: { $in: requirementIds },
        groupId: mongoose.Types.ObjectId(groupId),
        archived: false
      })
      .populate('user', 'username firstName lastName avatar color')
      .populate('requirementId', 'elementId versions.title')
      .sort({ timestamp: -1 })
      .limit(limit)
      .skip(offset);

      return timeline;
    } catch (error) {
      console.error('Error getting project timeline:', error);
      throw error;
    }
  }

  /**
   * Archive old timeline events based on group retention policy
   */
  static async archiveOldEvents(groupId) {
    try {
      const Group = require('../models/Group');
      const group = await Group.findById(groupId);
      
      if (!group || !group.retentionPolicy.archivalEnabled) {
        return { archived: 0, message: 'Archival disabled for this group' };
      }

      const expirationDate = group.getTimelineExpirationDate();
      
      // Find events to archive
      const eventsToArchive = await TimelineEvent.find({
        groupId: mongoose.Types.ObjectId(groupId),
        timestamp: { $lt: expirationDate },
        archived: false
      });

      let archivedCount = 0;

      // Use transaction for consistency
      const session = await mongoose.startSession();
      
      try {
        await session.withTransaction(async () => {
          for (const event of eventsToArchive) {
            // Create archived version
            const archivedEvent = new ArchivedTimelineEvent({
              originalEventId: event._id,
              originalRequirementId: event.requirementId,
              groupId: event.groupId,
              retentionTier: group.retentionPolicy.tier,
              eventData: {
                eventType: event.eventType,
                timestamp: event.timestamp,
                user: event.user,
                version: event.version,
                eventData: event.eventData,
                description: event.description,
                originalCreatedAt: event.createdAt,
                originalUpdatedAt: event.updatedAt
              }
            });

            await archivedEvent.save({ session });
            await TimelineEvent.deleteOne({ _id: event._id }, { session });
            archivedCount++;
          }
        });
      } finally {
        await session.endSession();
      }

      // Update group's last archival date
      group.retentionPolicy.lastArchivalDate = new Date();
      await group.save();

      return {
        archived: archivedCount,
        message: `Successfully archived ${archivedCount} timeline events`
      };
    } catch (error) {
      console.error('Error archiving timeline events:', error);
      throw error;
    }
  }

  /**
   * Get timeline statistics for a requirement
   */
  static async getTimelineStats(requirementId, groupId) {
    try {
      const stats = await TimelineEvent.aggregate([
        {
          $match: {
            requirementId: mongoose.Types.ObjectId(requirementId),
            groupId: mongoose.Types.ObjectId(groupId),
            archived: false
          }
        },
        {
          $group: {
            _id: '$eventType',
            count: { $sum: 1 },
            lastEvent: { $max: '$timestamp' }
          }
        },
        {
          $sort: { count: -1 }
        }
      ]);

      const totalEvents = await TimelineEvent.countDocuments({
        requirementId: mongoose.Types.ObjectId(requirementId),
        groupId: mongoose.Types.ObjectId(groupId),
        archived: false
      });

      return {
        totalEvents,
        eventTypes: stats
      };
    } catch (error) {
      console.error('Error getting timeline stats:', error);
      throw error;
    }
  }
}

module.exports = TimelineService;

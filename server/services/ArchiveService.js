const mongoose = require('mongoose');
const TimelineService = require('./TimelineService');
const Group = require('../models/Group');

class ArchiveService {
  /**
   * Check all groups for retention warnings and archival needs
   */
  static async checkAllGroups() {
    try {
      const groups = await Group.find({ 
        status: 'active',
        'retentionPolicy.archivalEnabled': true 
      });

      const results = {
        warnings: [],
        archival: [],
        errors: []
      };

      for (const group of groups) {
        try {
          // Check for warnings
          const warningCheck = group.needsRetentionWarning();
          if (warningCheck.needsWarning) {
            results.warnings.push({
              groupId: group._id,
              groupName: group.name,
              daysUntilExpiration: warningCheck.daysUntilExpiration,
              warningDate: warningCheck.warningDate
            });
          }

          // Check if archival is needed
          if (this.shouldRunArchival(group)) {
            const archivalResult = await TimelineService.archiveOldEvents(group._id);
            results.archival.push({
              groupId: group._id,
              groupName: group.name,
              ...archivalResult
            });
          }
        } catch (error) {
          results.errors.push({
            groupId: group._id,
            groupName: group.name,
            error: error.message
          });
        }
      }

      return results;
    } catch (error) {
      console.error('Error checking groups for archival:', error);
      throw error;
    }
  }

  /**
   * Determine if a group should run archival based on frequency
   */
  static shouldRunArchival(group) {
    if (!group.retentionPolicy.archivalEnabled) {
      return false;
    }

    const lastArchival = group.retentionPolicy.lastArchivalDate;
    const frequency = group.retentionPolicy.archivalFrequency;
    
    if (!lastArchival) {
      return true; // Never archived before
    }

    const now = new Date();
    const daysSinceLastArchival = Math.floor((now - lastArchival) / (1000 * 60 * 60 * 24));

    switch (frequency) {
      case 'daily':
        return daysSinceLastArchival >= 1;
      case 'weekly':
        return daysSinceLastArchival >= 7;
      case 'monthly':
        return daysSinceLastArchival >= 30;
      default:
        return false;
    }
  }

  /**
   * Send retention warnings to group administrators
   */
  static async sendRetentionWarnings(warnings) {
    // Mock implementation - in production this would integrate with email service
    console.log('=== RETENTION WARNINGS ===');
    
    for (const warning of warnings) {
      console.log(`
Group: ${warning.groupName}
Days until expiration: ${warning.daysUntilExpiration}
Action needed: Purchase more retention time
URL: /admin/extend-retention?groupId=${warning.groupId}
      `);
      
      // Update last warning date
      await Group.findByIdAndUpdate(warning.groupId, {
        'retentionPolicy.lastWarningDate': new Date()
      });
    }
    
    return {
      sent: warnings.length,
      message: `Sent ${warnings.length} retention warnings`
    };
  }

  /**
   * Get archival statistics for admin dashboard
   */
  static async getArchivalStats() {
    try {
      const ArchivedTimelineEvent = require('../models/ArchivedTimelineEvent');
      
      const stats = await ArchivedTimelineEvent.aggregate([
        {
          $group: {
            _id: '$retentionTier',
            count: { $sum: 1 },
            oldestArchived: { $min: '$archivedAt' },
            newestArchived: { $max: '$archivedAt' }
          }
        },
        {
          $sort: { count: -1 }
        }
      ]);

      const totalArchived = await ArchivedTimelineEvent.countDocuments();
      
      return {
        totalArchived,
        byTier: stats
      };
    } catch (error) {
      console.error('Error getting archival stats:', error);
      throw error;
    }
  }

  /**
   * Restore archived events for a specific requirement (emergency recovery)
   */
  static async restoreArchivedEvents(requirementId, groupId, options = {}) {
    try {
      const ArchivedTimelineEvent = require('../models/ArchivedTimelineEvent');
      const { 
        startDate = null, 
        endDate = null,
        eventTypes = null 
      } = options;

      const query = {
        originalRequirementId: mongoose.Types.ObjectId(requirementId),
        groupId: mongoose.Types.ObjectId(groupId)
      };

      if (startDate || endDate) {
        query['eventData.timestamp'] = {};
        if (startDate) query['eventData.timestamp'].$gte = new Date(startDate);
        if (endDate) query['eventData.timestamp'].$lte = new Date(endDate);
      }

      if (eventTypes && Array.isArray(eventTypes)) {
        query['eventData.eventType'] = { $in: eventTypes };
      }

      const archivedEvents = await ArchivedTimelineEvent.find(query);
      let restoredCount = 0;

      for (const archivedEvent of archivedEvents) {
        await archivedEvent.restore();
        restoredCount++;
      }

      return {
        restored: restoredCount,
        message: `Successfully restored ${restoredCount} archived events`
      };
    } catch (error) {
      console.error('Error restoring archived events:', error);
      throw error;
    }
  }

  /**
   * Get retention policy recommendations based on group usage
   */
  static async getRetentionRecommendations(groupId) {
    try {
      const TimelineEvent = require('../models/TimelineEvent');
      const group = await Group.findById(groupId);
      
      if (!group) {
        throw new Error('Group not found');
      }

      // Analyze timeline usage patterns
      const usageStats = await TimelineEvent.aggregate([
        {
          $match: {
            groupId: mongoose.Types.ObjectId(groupId),
            archived: false
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$timestamp' },
              month: { $month: '$timestamp' }
            },
            eventCount: { $sum: 1 },
            uniqueRequirements: { $addToSet: '$requirementId' },
            uniqueUsers: { $addToSet: '$user' }
          }
        },
        {
          $sort: { '_id.year': -1, '_id.month': -1 }
        },
        {
          $limit: 12 // Last 12 months
        }
      ]);

      const currentPolicy = group.getRetentionPolicy();
      const averageEventsPerMonth = usageStats.reduce((sum, stat) => sum + stat.eventCount, 0) / usageStats.length;
      
      let recommendation = {
        currentTier: group.retentionPolicy.tier,
        currentRetentionMonths: currentPolicy.retentionMonths,
        recommendedTier: group.retentionPolicy.tier,
        reason: 'Current policy is appropriate',
        estimatedCostSavings: 0,
        usagePattern: 'normal'
      };

      // High usage pattern - recommend longer retention
      if (averageEventsPerMonth > 1000) {
        recommendation.usagePattern = 'high';
        if (group.retentionPolicy.tier === 'small') {
          recommendation.recommendedTier = 'medium';
          recommendation.reason = 'High activity suggests need for longer retention';
        }
      }

      // Low usage pattern - could save money with shorter retention
      if (averageEventsPerMonth < 50) {
        recommendation.usagePattern = 'low';
        recommendation.reason = 'Low activity - current retention may be sufficient';
      }

      return {
        recommendation,
        usageStats,
        totalEvents: await TimelineEvent.countDocuments({
          groupId: mongoose.Types.ObjectId(groupId),
          archived: false
        })
      };
    } catch (error) {
      console.error('Error getting retention recommendations:', error);
      throw error;
    }
  }

  /**
   * Mock purchase more time functionality
   */
  static async mockPurchaseMoreTime(groupId, extensionYears) {
    try {
      const group = await Group.findById(groupId);
      if (!group) {
        throw new Error('Group not found');
      }

      // Mock pricing
      const pricing = {
        1: { price: 99, description: '1 Year Extension' },
        2: { price: 179, description: '2 Year Extension (10% discount)' },
        3: { price: 249, description: '3 Year Extension (15% discount)' }
      };

      const selectedPlan = pricing[extensionYears];
      if (!selectedPlan) {
        throw new Error('Invalid extension period');
      }

      // In production, this would integrate with payment processor
      // For now, just return mock purchase details
      return {
        success: true,
        orderId: `ORDER-${Date.now()}`,
        groupId: groupId,
        extensionYears: extensionYears,
        price: selectedPlan.price,
        description: selectedPlan.description,
        newExpirationDate: new Date(Date.now() + (extensionYears * 365 * 24 * 60 * 60 * 1000)),
        paymentUrl: `/admin/payment/confirm?orderId=ORDER-${Date.now()}`,
        message: 'Mock purchase created - payment integration needed for production'
      };
    } catch (error) {
      console.error('Error creating mock purchase:', error);
      throw error;
    }
  }
}

module.exports = ArchiveService;

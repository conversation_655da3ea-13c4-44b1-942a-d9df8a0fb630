/**
 * Cache Performance Monitoring Utility
 * Tracks cache hit/miss rates and performance metrics
 */

class CacheMetrics {
  constructor() {
    this.metrics = {
      objectIdCache: {
        hits: 0,
        misses: 0,
        totalRequests: 0,
        totalTime: 0,
        avgTime: 0
      },
      elementIdCache: {
        hits: 0,
        misses: 0,
        totalRequests: 0,
        totalTime: 0,
        avgTime: 0
      },
      databaseQueries: {
        objectIdQueries: 0,
        elementIdQueries: 0,
        totalTime: 0,
        avgTime: 0
      }
    };
    
    this.startTime = Date.now();
  }

  recordCacheHit(cacheType, responseTime = 0) {
    if (this.metrics[cacheType]) {
      this.metrics[cacheType].hits++;
      this.metrics[cacheType].totalRequests++;
      this.metrics[cacheType].totalTime += responseTime;
      this.metrics[cacheType].avgTime = this.metrics[cacheType].totalTime / this.metrics[cacheType].totalRequests;
    }
  }

  recordCacheMiss(cacheType, responseTime = 0) {
    if (this.metrics[cacheType]) {
      this.metrics[cacheType].misses++;
      this.metrics[cacheType].totalRequests++;
      this.metrics[cacheType].totalTime += responseTime;
      this.metrics[cacheType].avgTime = this.metrics[cacheType].totalTime / this.metrics[cacheType].totalRequests;
    }
  }

  recordDatabaseQuery(queryType, responseTime = 0) {
    this.metrics.databaseQueries[queryType]++;
    this.metrics.databaseQueries.totalTime += responseTime;
    const totalQueries = this.metrics.databaseQueries.objectIdQueries + this.metrics.databaseQueries.elementIdQueries;
    this.metrics.databaseQueries.avgTime = this.metrics.databaseQueries.totalTime / totalQueries;
  }

  getCacheHitRate(cacheType) {
    const cache = this.metrics[cacheType];
    if (cache.totalRequests === 0) return 0;
    return (cache.hits / cache.totalRequests * 100).toFixed(2);
  }

  getPerformanceReport() {
    const uptime = Date.now() - this.startTime;
    
    return {
      uptime: `${Math.floor(uptime / 1000)}s`,
      objectIdCache: {
        hitRate: `${this.getCacheHitRate('objectIdCache')}%`,
        hits: this.metrics.objectIdCache.hits,
        misses: this.metrics.objectIdCache.misses,
        totalRequests: this.metrics.objectIdCache.totalRequests,
        avgResponseTime: `${this.metrics.objectIdCache.avgTime.toFixed(2)}ms`
      },
      elementIdCache: {
        hitRate: `${this.getCacheHitRate('elementIdCache')}%`,
        hits: this.metrics.elementIdCache.hits,
        misses: this.metrics.elementIdCache.misses,
        totalRequests: this.metrics.elementIdCache.totalRequests,
        avgResponseTime: `${this.metrics.elementIdCache.avgTime.toFixed(2)}ms`
      },
      databaseQueries: {
        objectIdQueries: this.metrics.databaseQueries.objectIdQueries,
        elementIdQueries: this.metrics.databaseQueries.elementIdQueries,
        avgQueryTime: `${this.metrics.databaseQueries.avgTime.toFixed(2)}ms`
      },
      performance: {
        cacheEfficiency: this.calculateCacheEfficiency(),
        estimatedTimeSaved: this.calculateTimeSaved()
      }
    };
  }

  calculateCacheEfficiency() {
    const totalHits = this.metrics.objectIdCache.hits + this.metrics.elementIdCache.hits;
    const totalRequests = this.metrics.objectIdCache.totalRequests + this.metrics.elementIdCache.totalRequests;
    
    if (totalRequests === 0) return '0%';
    return `${(totalHits / totalRequests * 100).toFixed(2)}%`;
  }

  calculateTimeSaved() {
    // Estimate time saved by caching (assuming database queries are 10x slower than cache hits)
    const cacheHitTime = 1; // Assume 1ms for cache hits
    const dbQueryTime = 10; // Assume 10ms for database queries
    
    const totalHits = this.metrics.objectIdCache.hits + this.metrics.elementIdCache.hits;
    const timeSaved = totalHits * (dbQueryTime - cacheHitTime);
    
    return `${timeSaved}ms`;
  }

  reset() {
    this.metrics = {
      objectIdCache: { hits: 0, misses: 0, totalRequests: 0, totalTime: 0, avgTime: 0 },
      elementIdCache: { hits: 0, misses: 0, totalRequests: 0, totalTime: 0, avgTime: 0 },
      databaseQueries: { objectIdQueries: 0, elementIdQueries: 0, totalTime: 0, avgTime: 0 }
    };
    this.startTime = Date.now();
  }

  logPerformanceReport() {
    const report = this.getPerformanceReport();
    console.log('\n=== ElementID Cache Performance Report ===');
    console.log(`Uptime: ${report.uptime}`);
    console.log('\nObjectID Cache:');
    console.log(`  Hit Rate: ${report.objectIdCache.hitRate}`);
    console.log(`  Hits: ${report.objectIdCache.hits}, Misses: ${report.objectIdCache.misses}`);
    console.log(`  Avg Response Time: ${report.objectIdCache.avgResponseTime}`);
    console.log('\nElementID Cache:');
    console.log(`  Hit Rate: ${report.elementIdCache.hitRate}`);
    console.log(`  Hits: ${report.elementIdCache.hits}, Misses: ${report.elementIdCache.misses}`);
    console.log(`  Avg Response Time: ${report.elementIdCache.avgResponseTime}`);
    console.log('\nDatabase Queries:');
    console.log(`  ObjectID Queries: ${report.databaseQueries.objectIdQueries}`);
    console.log(`  ElementID Queries: ${report.databaseQueries.elementIdQueries}`);
    console.log(`  Avg Query Time: ${report.databaseQueries.avgQueryTime}`);
    console.log('\nOverall Performance:');
    console.log(`  Cache Efficiency: ${report.performance.cacheEfficiency}`);
    console.log(`  Estimated Time Saved: ${report.performance.estimatedTimeSaved}`);
    console.log('==========================================\n');
  }
}

// Global instance
const cacheMetrics = new CacheMetrics();

// Log performance report every 5 minutes in development
if (process.env.NODE_ENV === 'development') {
  setInterval(() => {
    if (cacheMetrics.metrics.objectIdCache.totalRequests > 0 || cacheMetrics.metrics.elementIdCache.totalRequests > 0) {
      cacheMetrics.logPerformanceReport();
    }
  }, 5 * 60 * 1000); // 5 minutes
}

module.exports = cacheMetrics;

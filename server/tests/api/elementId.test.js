const request = require('supertest');
const app = require('../../app');
const { createTestSetup, createTestUser, createTestGroup } = require('../helpers/testHelpers');
const Project = require('../../models/Project');
const Feature = require('../../models/Feature');
const Requirement = require('../../models/Requirement');

describe('ElementID/ObjectID Hybrid API Tests', () => {
  let testSetup;
  let testProject;
  let testFeature;
  let testRequirement;

  beforeAll(async () => {
    testSetup = await createTestSetup();
    
    // Create test project
    testProject = new Project({
      name: 'Test Project for ElementID',
      description: 'Test project for ElementID testing',
      group: testSetup.group._id,
      createdBy: testSetup.adminUser._id,
      elementId: 'TEST-001'
    });
    await testProject.save();

    // Create test feature
    testFeature = new Feature({
      project: testProject._id,
      group: testSetup.group._id,
      elementId: 'TEST-F-001',
      versions: [{
        version: 1,
        title: 'Test Feature',
        description: 'Test feature for ElementID testing',
        createdBy: testSetup.adminUser._id
      }],
      createdBy: testSetup.adminUser._id
    });
    await testFeature.save();

    // Create test requirement
    testRequirement = new Requirement({
      project: testProject._id,
      feature: testFeature._id,
      group: testSetup.group._id,
      elementId: 'TEST-R-001',
      type: 'requirement',
      versions: [{
        version: 1,
        title: 'Test Requirement',
        description: 'Test requirement for ElementID testing',
        createdBy: testSetup.adminUser._id
      }],
      createdBy: testSetup.adminUser._id
    });
    await testRequirement.save();
  });

  describe('Project API - ElementID vs ObjectID', () => {
    test('should fetch project using ObjectID', async () => {
      const response = await request(app)
        .get(`/api/projects/${testProject._id}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body._id).toBe(testProject._id.toString());
      expect(response.body.elementId).toBe('TEST-001');
    });

    test('should fetch project using ElementID', async () => {
      const response = await request(app)
        .get(`/api/projects/TEST-001`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body._id).toBe(testProject._id.toString());
      expect(response.body.elementId).toBe('TEST-001');
    });

    test('should return 404 for invalid ElementID', async () => {
      const response = await request(app)
        .get('/api/projects/INVALID-001')
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.message).toContain('not found');
    });

    test('should return 400 for malformed ElementID', async () => {
      const response = await request(app)
        .get('/api/projects/invalid-format')
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Invalid ID format');
    });
  });

  describe('Feature API - ElementID vs ObjectID', () => {
    test('should fetch feature using ObjectID', async () => {
      const response = await request(app)
        .get(`/api/features/${testFeature._id}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body._id).toBe(testFeature._id.toString());
      expect(response.body.elementId).toBe('TEST-F-001');
    });

    test('should fetch feature using ElementID', async () => {
      const response = await request(app)
        .get(`/api/features/TEST-F-001`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body._id).toBe(testFeature._id.toString());
      expect(response.body.elementId).toBe('TEST-F-001');
    });

    test('should respect multi-tenancy with ElementID', async () => {
      // Create another group and feature with same ElementID
      const otherGroup = await createTestGroup('Other Group', 'other-group', '1-3', testSetup.superUser);
      const otherUser = await createTestUser({
        username: 'otheruser',
        email: '<EMAIL>',
        groupStatus: 'active'
      }, otherGroup);

      const otherProject = new Project({
        name: 'Other Project',
        description: 'Other project for testing',
        group: otherGroup._id,
        createdBy: otherUser._id,
        elementId: 'TEST-002'
      });
      await otherProject.save();

      const otherFeature = new Feature({
        project: otherProject._id,
        group: otherGroup._id,
        elementId: 'TEST-F-001', // Same ElementID, different group
        versions: [{
          version: 1,
          title: 'Other Feature',
          description: 'Feature in other group',
          createdBy: otherUser._id
        }],
        createdBy: otherUser._id
      });
      await otherFeature.save();

      // User from first group should only see their feature
      const response = await request(app)
        .get('/api/features/TEST-F-001')
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body._id).toBe(testFeature._id.toString());
      expect(response.body.group).toBe(testSetup.group._id.toString());
    });
  });

  describe('Requirement API - ElementID vs ObjectID', () => {
    test('should fetch requirement using ObjectID', async () => {
      const response = await request(app)
        .get(`/api/requirements/${testRequirement._id}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body._id).toBe(testRequirement._id.toString());
      expect(response.body.elementId).toBe('TEST-R-001');
    });

    test('should fetch requirement using ElementID', async () => {
      const response = await request(app)
        .get(`/api/requirements/TEST-R-001`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body._id).toBe(testRequirement._id.toString());
      expect(response.body.elementId).toBe('TEST-R-001');
    });

    test('should fetch requirement children using ElementID', async () => {
      const response = await request(app)
        .get(`/api/requirements/TEST-R-001/children`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('Cross-Reference Tests', () => {
    test('should handle feature children lookup with ElementID', async () => {
      const response = await request(app)
        .get(`/api/requirements/TEST-F-001/children`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].feature.toString()).toBe(testFeature._id.toString());
    });

    test('should handle mixed ObjectID/ElementID in nested operations', async () => {
      // Update requirement using ElementID
      const updateData = {
        title: 'Updated via ElementID',
        description: 'Updated using ElementID instead of ObjectID'
      };

      const response = await request(app)
        .put(`/api/requirements/TEST-R-001`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.versions[0].title).toBe(updateData.title);
    });
  });

  describe('Error Handling', () => {
    test('should handle ElementID not found in user group', async () => {
      const response = await request(app)
        .get('/api/features/NONEXIST-F-001')
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.message).toContain('not found');
    });

    test('should validate ElementID format correctly', async () => {
      const invalidIds = [
        'invalid',
        '123-456',
        'TEST-X-001', // Invalid type
        'TEST-F-', // Missing sequence
        'TEST-F-ABC' // Non-numeric sequence
      ];

      for (const invalidId of invalidIds) {
        const response = await request(app)
          .get(`/api/features/${invalidId}`)
          .set('Authorization', `Bearer ${testSetup.adminToken}`);

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('Invalid');
      }
    });
  });
});

const request = require('supertest');
const app = require('../../app');
const { createTestSetup, createTestGroup, createTestUser } = require('../helpers/testHelpers');
const Project = require('../../models/Project');
const Feature = require('../../models/Feature');
const Requirement = require('../../models/Requirement');

describe('Security Fix Tests - ObjectID Group Filtering', () => {
  let testSetup;
  let otherGroupSetup;
  let testProject;
  let otherProject;

  beforeEach(async () => {
    // Create first group and user
    testSetup = await createTestSetup();
    
    // Create second group and user
    const otherGroup = await createTestGroup('Other Group', 'other-group', '1-3', testSetup.superUser);
    const otherUser = await createTestUser({
      username: 'otheruser',
      email: '<EMAIL>',
      groupStatus: 'active'
    }, otherGroup);

    otherGroupSetup = {
      group: otherGroup,
      user: otherUser,
      token: require('../helpers/testHelpers').generateAuthToken(otherUser)
    };

    // Create project in first group
    testProject = new Project({
      name: 'Test Group Project',
      description: 'Project in test group',
      group: testSetup.group._id,
      createdBy: testSetup.adminUser._id,
      elementId: 'TEST-001'
    });
    await testProject.save();

    // Create project in second group
    otherProject = new Project({
      name: 'Other Group Project', 
      description: 'Project in other group',
      group: otherGroupSetup.group._id,
      createdBy: otherGroupSetup.user._id,
      elementId: 'OTHER-001'
    });
    await otherProject.save();
  });

  describe('Cross-Group Access Prevention', () => {
    test('should allow access to own group project via ObjectID', async () => {
      const response = await request(app)
        .get(`/api/projects/${testProject._id}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body._id).toBe(testProject._id.toString());
      expect(response.body.group).toBe(testSetup.group._id.toString());
    });

    test('should prevent access to other group project via ObjectID', async () => {
      const response = await request(app)
        .get(`/api/projects/${otherProject._id}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.message).toContain('not found');
    });

    test('should allow access to own group project via ElementID', async () => {
      const response = await request(app)
        .get(`/api/projects/TEST-001`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body._id).toBe(testProject._id.toString());
      expect(response.body.elementId).toBe('TEST-001');
    });

    test('should prevent access to other group project via ElementID', async () => {
      const response = await request(app)
        .get(`/api/projects/OTHER-001`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.message).toContain('not found');
    });
  });

  describe('Super User Access', () => {
    test('should allow super user to access any project via ObjectID', async () => {
      const response = await request(app)
        .get(`/api/projects/${otherProject._id}`)
        .set('Authorization', `Bearer ${testSetup.superUserToken}`);

      expect(response.status).toBe(200);
      expect(response.body._id).toBe(otherProject._id.toString());
      expect(response.body.group).toBe(otherGroupSetup.group._id.toString());
    });

    test('should allow super user to access any project via ElementID', async () => {
      const response = await request(app)
        .get(`/api/projects/OTHER-001`)
        .set('Authorization', `Bearer ${testSetup.superUserToken}`);

      expect(response.status).toBe(200);
      expect(response.body._id).toBe(otherProject._id.toString());
      expect(response.body.elementId).toBe('OTHER-001');
    });
  });

  describe('Bidirectional Access Control', () => {
    test('other group user should access their own project via ObjectID', async () => {
      const response = await request(app)
        .get(`/api/projects/${otherProject._id}`)
        .set('Authorization', `Bearer ${otherGroupSetup.token}`);

      expect(response.status).toBe(200);
      expect(response.body._id).toBe(otherProject._id.toString());
    });

    test('other group user should NOT access test group project via ObjectID', async () => {
      const response = await request(app)
        .get(`/api/projects/${testProject._id}`)
        .set('Authorization', `Bearer ${otherGroupSetup.token}`);

      expect(response.status).toBe(404);
      expect(response.body.message).toContain('not found');
    });
  });

  describe('Security Validation', () => {
    test('should validate that ObjectID security fix is working', async () => {
      // This test ensures that the security vulnerability is fixed
      // Before the fix: ObjectIDs would bypass group filtering
      // After the fix: ObjectIDs should respect group boundaries
      
      console.log('Test Project ID:', testProject._id.toString());
      console.log('Test Project Group:', testProject.group.toString());
      console.log('Other Project ID:', otherProject._id.toString());
      console.log('Other Project Group:', otherProject.group.toString());
      
      // User from group 1 tries to access project from group 2
      const crossGroupResponse = await request(app)
        .get(`/api/projects/${otherProject._id}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      // This should fail (404) proving the security fix works
      expect(crossGroupResponse.status).toBe(404);
      
      // User from group 1 accesses their own project
      const ownGroupResponse = await request(app)
        .get(`/api/projects/${testProject._id}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      // This should succeed (200) proving normal functionality works
      expect(ownGroupResponse.status).toBe(200);
      
      console.log('✅ Security fix validated: ObjectIDs now respect group boundaries');
    });
  });
});

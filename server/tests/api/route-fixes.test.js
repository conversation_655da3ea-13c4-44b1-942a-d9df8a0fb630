const request = require('supertest');
const app = require('../../app');
const { createTestSetup } = require('../helpers/testHelpers');
const Feature = require('../../models/Feature');
const Requirement = require('../../models/Requirement');

describe('Route Fixes - ElementID Resolution', () => {
  let testSetup;
  let testFeature;
  let testRequirement;

  beforeEach(async () => {
    testSetup = await createTestSetup();
    
    // Create a test feature
    testFeature = new Feature({
      title: 'Test Feature for Route Fixes',
      description: 'Feature for testing route fixes',
      project: testSetup.project._id,
      group: testSetup.group._id,
      createdBy: testSetup.adminUser._id,
      elementId: 'ROUTE-F-001',
      versions: [{
        title: 'Test Feature for Route Fixes',
        description: 'Feature for testing route fixes',
        createdBy: testSetup.adminUser._id,
        createdAt: new Date()
      }]
    });
    await testFeature.save();

    // Create a test requirement under the feature
    testRequirement = new Requirement({
      title: 'Test Requirement for Route Fixes',
      description: 'Requirement for testing route fixes',
      project: testSetup.project._id,
      feature: testFeature._id,
      group: testSetup.group._id,
      createdBy: testSetup.adminUser._id,
      elementId: 'ROUTE-R-001',
      type: 'requirement',
      versions: [{
        title: 'Test Requirement for Route Fixes',
        description: 'Requirement for testing route fixes',
        createdBy: testSetup.adminUser._id,
        createdAt: new Date()
      }]
    });
    await testRequirement.save();
  });

  describe('Features linked-texts routes', () => {
    test('should get linked texts using ElementID', async () => {
      const response = await request(app)
        .get(`/api/features/${testFeature.elementId}/linked-texts?version=1`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      console.log('Linked texts ElementID response:', response.status, response.body?.message || 'Success');
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
    });

    test('should get linked texts using ObjectID', async () => {
      const response = await request(app)
        .get(`/api/features/${testFeature._id}/linked-texts?version=1`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      console.log('Linked texts ObjectID response:', response.status, response.body?.message || 'Success');
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
    });

    test('should add linked text using ElementID', async () => {
      const linkedTextData = {
        text: 'Test linked text',
        startIndex: 0,
        endIndex: 4,
        version: 1
      };

      const response = await request(app)
        .post(`/api/features/${testFeature.elementId}/linked-texts`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send(linkedTextData);

      console.log('Add linked text ElementID response:', response.status, response.body?.message || 'Success');
      
      expect(response.status).toBe(200);
    });
  });

  describe('Requirements children routes', () => {
    test('should get children using Feature ElementID', async () => {
      const response = await request(app)
        .get(`/api/requirements/${testFeature.elementId}/children`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      console.log('Children Feature ElementID response:', response.status, response.body?.message || 'Success');
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].elementId).toBe(testRequirement.elementId);
    });

    test('should get children using Feature ObjectID', async () => {
      const response = await request(app)
        .get(`/api/requirements/${testFeature._id}/children`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      console.log('Children Feature ObjectID response:', response.status, response.body?.message || 'Success');
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });

    test('should get children using Requirement ElementID', async () => {
      const response = await request(app)
        .get(`/api/requirements/${testRequirement.elementId}/children`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      console.log('Children Requirement ElementID response:', response.status, response.body?.message || 'Success');
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      // Should be empty since this requirement has no children
    });
  });

  describe('Error handling', () => {
    test('should return 404 for non-existent ElementID in linked-texts', async () => {
      const response = await request(app)
        .get('/api/features/FAKE-F-999/linked-texts?version=1')
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      console.log('Non-existent linked-texts response:', response.status, response.body?.message);
      
      expect(response.status).toBe(404);
      expect(response.body.message).toContain('not found');
    });

    test('should return 404 for non-existent ElementID in children', async () => {
      const response = await request(app)
        .get('/api/requirements/FAKE-F-999/children')
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      console.log('Non-existent children response:', response.status, response.body?.message);
      
      expect(response.status).toBe(404);
      expect(response.body.message).toContain('not found');
    });
  });

  describe('Performance validation', () => {
    test('should perform similarly with ObjectID vs ElementID for linked-texts', async () => {
      const iterations = 3;
      let objectIdTotalTime = 0;
      let elementIdTotalTime = 0;
      
      // Test ObjectID performance
      for (let i = 0; i < iterations; i++) {
        const start = Date.now();
        await request(app)
          .get(`/api/features/${testFeature._id}/linked-texts?version=1`)
          .set('Authorization', `Bearer ${testSetup.adminToken}`);
        objectIdTotalTime += Date.now() - start;
      }
      
      // Test ElementID performance
      for (let i = 0; i < iterations; i++) {
        const start = Date.now();
        await request(app)
          .get(`/api/features/${testFeature.elementId}/linked-texts?version=1`)
          .set('Authorization', `Bearer ${testSetup.adminToken}`);
        elementIdTotalTime += Date.now() - start;
      }
      
      const objectIdAvg = objectIdTotalTime / iterations;
      const elementIdAvg = elementIdTotalTime / iterations;
      
      console.log(`Linked-texts ObjectID avg: ${objectIdAvg}ms`);
      console.log(`Linked-texts ElementID avg: ${elementIdAvg}ms`);
      console.log(`Performance ratio: ${(elementIdAvg / objectIdAvg).toFixed(2)}x`);
      
      // Both should be reasonably fast
      expect(objectIdAvg).toBeLessThan(1000); // Less than 1 second
      expect(elementIdAvg).toBeLessThan(1000); // Less than 1 second
      
      console.log('✅ Route fixes performance validation: Both ObjectID and ElementID routes are fast');
    });
  });
});

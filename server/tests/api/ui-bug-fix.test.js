const request = require('supertest');
const app = require('../../app');

describe('UI Bug Fix - Route ElementID Resolution', () => {
  // Test the specific routes that were failing in the UI
  // These tests verify that the routes don't crash and return proper responses

  describe('Features linked-texts routes', () => {
    test('should handle ElementID in linked-texts route without crashing', async () => {
      // Test the route that was failing: /api/features/HP-F-001/linked-texts?version=1
      const response = await request(app)
        .get('/api/features/HP-F-001/linked-texts?version=1');

      console.log('Linked-texts route response:', response.status);
      
      // Should not crash (500 error) - should return 401 (auth required) or 404 (not found)
      expect(response.status).not.toBe(500);
      expect([401, 404]).toContain(response.status);
    });

    test('should handle ObjectID in linked-texts route without crashing', async () => {
      const mongoose = require('mongoose');
      const fakeObjectId = new mongoose.Types.ObjectId();
      
      const response = await request(app)
        .get(`/api/features/${fakeObjectId}/linked-texts?version=1`);

      console.log('Linked-texts ObjectID route response:', response.status);
      
      // Should not crash (500 error)
      expect(response.status).not.toBe(500);
      expect([401, 404]).toContain(response.status);
    });
  });

  describe('Requirements children routes', () => {
    test('should handle ElementID in children route without crashing', async () => {
      // Test the route that was failing: /api/requirements/HP-F-001/children
      const response = await request(app)
        .get('/api/requirements/HP-F-001/children');

      console.log('Children route response:', response.status);
      
      // Should not crash (500 error) - should return 401 (auth required) or 404 (not found)
      expect(response.status).not.toBe(500);
      expect([401, 404]).toContain(response.status);
    });

    test('should handle ObjectID in children route without crashing', async () => {
      const mongoose = require('mongoose');
      const fakeObjectId = new mongoose.Types.ObjectId();
      
      const response = await request(app)
        .get(`/api/requirements/${fakeObjectId}/children`);

      console.log('Children ObjectID route response:', response.status);
      
      // Should not crash (500 error)
      expect(response.status).not.toBe(500);
      expect([401, 404]).toContain(response.status);
    });
  });

  describe('Route middleware validation', () => {
    test('should validate ElementID format correctly', async () => {
      // Test invalid ElementID format
      const response = await request(app)
        .get('/api/features/invalid-format/linked-texts?version=1');

      console.log('Invalid ElementID format response:', response.status);
      
      // Should return 400 for invalid format or 401 for auth
      expect([400, 401]).toContain(response.status);
    });

    test('should handle empty ElementID gracefully', async () => {
      const response = await request(app)
        .get('/api/features//linked-texts?version=1');

      console.log('Empty ElementID response:', response.status);
      
      // Should handle gracefully (not crash)
      expect(response.status).not.toBe(500);
    });
  });

  describe('Performance validation', () => {
    test('should respond quickly to route requests', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/api/features/HP-F-001/linked-texts?version=1');
      
      const responseTime = Date.now() - start;
      
      console.log(`Route response time: ${responseTime}ms`);
      
      // Should respond within reasonable time (not hang)
      expect(responseTime).toBeLessThan(5000); // 5 seconds max
      expect(response.status).not.toBe(500); // Should not crash
    });
  });
});

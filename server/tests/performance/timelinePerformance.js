const mongoose = require('mongoose');
const TimelineService = require('../../services/TimelineService');
const Requirement = require('../../models/Requirement');
const User = require('../../models/User');
const Group = require('../../models/Group');
require('dotenv').config();

async function measureTimelinePerformance() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('🔍 MEASURING TIMELINE PERFORMANCE IMPACT\n');
    
    // Find existing data with proper relationships
    const requirement = await Requirement.findOne({}).populate('group');
    const user = await User.findOne({});

    if (!requirement || !user || !requirement.group) {
      console.log('❌ No existing data found. Please run tests first.');
      await mongoose.disconnect();
      return;
    }

    const group = requirement.group;
    
    console.log('✅ Using existing data for performance test');
    console.log(`   Requirement: ${requirement.elementId}`);
    console.log(`   User: ${user.username}`);
    console.log(`   Group: ${group.name}\n`);
    
    // Test 1: Single timeline event creation
    console.log('📊 TEST 1: Single Timeline Event Creation');
    const singleEventTimes = [];
    
    for (let i = 0; i < 5; i++) {
      const startTime = Date.now();
      
      try {
        await TimelineService.addEvent(
          requirement._id,
          'comment_added',
          user._id,
          group._id,
          { comment: `Performance test comment ${i + 1}` },
          1
        );
        
        const duration = Date.now() - startTime;
        singleEventTimes.push(duration);
        console.log(`   Event ${i + 1}: ${duration}ms`);
      } catch (error) {
        console.log(`   Event ${i + 1}: FAILED - ${error.message}`);
      }
    }
    
    if (singleEventTimes.length > 0) {
      const avgSingle = singleEventTimes.reduce((a, b) => a + b, 0) / singleEventTimes.length;
      console.log(`   Average: ${avgSingle.toFixed(2)}ms\n`);
    }
    
    // Test 2: Multiple timeline events (simulating requirement update)
    console.log('📊 TEST 2: Multiple Timeline Events (Requirement Update Simulation)');
    const multiEventStart = Date.now();
    
    try {
      // Simulate what happens during requirement update
      await TimelineService.addEvent(requirement._id, 'version_created', user._id, group._id, { version: 2 }, 2);
      await TimelineService.addEvent(requirement._id, 'state_changed', user._id, group._id, { from: 'New', to: 'Draft' }, 2);
      await TimelineService.addEvent(requirement._id, 'tag_added', user._id, group._id, { tag: 'urgent' }, 2);
      
      const multiEventTime = Date.now() - multiEventStart;
      console.log(`   3 timeline events: ${multiEventTime}ms\n`);
      
      // Test 3: Database lookup performance (what happens during requirement loading)
      console.log('📊 TEST 3: Requirement Lookup Performance');
      const lookupStart = Date.now();
      
      const foundRequirement = await Requirement.findOne({
        _id: requirement._id,
        group: group._id
      });
      
      const lookupTime = Date.now() - lookupStart;
      console.log(`   Requirement lookup: ${lookupTime}ms\n`);
      
      // Test 4: Timeline retrieval performance
      console.log('📊 TEST 4: Timeline Retrieval Performance');
      const timelineStart = Date.now();
      
      const timeline = await TimelineService.getRequirementTimeline(
        requirement._id,
        group._id,
        { limit: 20 }
      );
      
      const timelineTime = Date.now() - timelineStart;
      console.log(`   Timeline retrieval (${timeline.length} events): ${timelineTime}ms\n`);
      
      // Analysis and recommendations
      console.log('🎯 PERFORMANCE ANALYSIS:\n');
      
      if (singleEventTimes.length > 0) {
        const avgSingle = singleEventTimes.reduce((a, b) => a + b, 0) / singleEventTimes.length;
        
        console.log('📈 Timeline Event Creation:');
        if (avgSingle > 200) {
          console.log('   🚨 CRITICAL: Very slow (>200ms average)');
          console.log('   ❌ WILL impact user experience significantly');
          console.log('   💡 Recommendation: Make timeline creation asynchronous');
        } else if (avgSingle > 100) {
          console.log('   ⚠️  WARNING: Slow (100-200ms average)');
          console.log('   ⚠️  Could impact user experience');
          console.log('   💡 Recommendation: Consider optimization or async processing');
        } else if (avgSingle > 50) {
          console.log('   ⚡ MODERATE: Acceptable (50-100ms average)');
          console.log('   ✅ Should not significantly impact user experience');
          console.log('   💡 Recommendation: Monitor performance, consider optimization');
        } else {
          console.log('   ✅ EXCELLENT: Fast (<50ms average)');
          console.log('   ✅ No impact on user experience');
        }
        console.log('');
      }
      
      console.log('📈 Multiple Events (Requirement Updates):');
      if (multiEventTime > 500) {
        console.log('   🚨 CRITICAL: Very slow (>500ms)');
        console.log('   ❌ Users will experience noticeable delays');
        console.log('   💡 Recommendation: Batch timeline events or use async processing');
      } else if (multiEventTime > 200) {
        console.log('   ⚠️  WARNING: Noticeable delay (200-500ms)');
        console.log('   ⚠️  May impact user experience during updates');
        console.log('   💡 Recommendation: Consider batching or async processing');
      } else {
        console.log('   ✅ ACCEPTABLE: Fast enough (<200ms)');
        console.log('   ✅ Should not significantly impact user experience');
      }
      console.log('');
      
      console.log('📈 Timeline Retrieval:');
      if (timelineTime > 300) {
        console.log('   🚨 CRITICAL: Very slow (>300ms)');
        console.log('   ❌ Will slow down requirement page loading');
        console.log('   💡 Recommendation: Add pagination, optimize queries');
      } else if (timelineTime > 100) {
        console.log('   ⚠️  WARNING: Slow (100-300ms)');
        console.log('   ⚠️  May slow down requirement page loading');
        console.log('   💡 Recommendation: Consider lazy loading or pagination');
      } else {
        console.log('   ✅ GOOD: Fast enough (<100ms)');
        console.log('   ✅ Should not impact requirement page loading');
      }
      console.log('');
      
      // Overall recommendation
      console.log('🎯 OVERALL RECOMMENDATION:\n');
      
      const totalImpact = (singleEventTimes.length > 0 ? singleEventTimes.reduce((a, b) => a + b, 0) / singleEventTimes.length : 0) + multiEventTime + timelineTime;
      
      if (totalImpact > 800) {
        console.log('🚨 HIGH IMPACT: Timeline system adds significant overhead');
        console.log('   Immediate action needed to prevent user experience degradation');
        console.log('   Consider making timeline operations asynchronous');
      } else if (totalImpact > 400) {
        console.log('⚠️  MODERATE IMPACT: Timeline system adds noticeable overhead');
        console.log('   Monitor user feedback and consider optimizations');
        console.log('   Timeline creation could be made asynchronous for better UX');
      } else {
        console.log('✅ LOW IMPACT: Timeline system overhead is acceptable');
        console.log('   Current implementation should not significantly impact users');
        console.log('   Continue monitoring performance as data grows');
      }
      
    } catch (error) {
      console.log('❌ Multiple events test failed:', error.message);
    }
    
    await mongoose.disconnect();
    
  } catch (error) {
    console.error('❌ Error:', error);
    await mongoose.disconnect();
  }
}

// Run the performance test
measureTimelinePerformance();

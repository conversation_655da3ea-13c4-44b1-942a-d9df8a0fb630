const request = require('supertest');
const app = require('../../app');
const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
require('dotenv').config();

async function testOptimizedTimelinePerformance() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('🚀 TESTING OPTIMIZED TIMELINE PERFORMANCE\n');
    
    // Find existing data
    const User = require('../../models/User');
    const Group = require('../../models/Group');
    const Project = require('../../models/Project');
    
    const user = await User.findOne({});
    const group = await Group.findOne({});
    const project = await Project.findOne({});
    
    if (!user || !group || !project) {
      console.log('❌ No test data found');
      await mongoose.disconnect();
      return;
    }
    
    console.log('✅ Test data found:');
    console.log(`   User: ${user.username}`);
    console.log(`   Group: ${group.name}`);
    console.log(`   Project: ${project.name}\n`);
    
    // Create a test token
    const token = jwt.sign(
      { userId: user._id, groupId: group._id },
      process.env.JWT_SECRET || 'test-secret'
    );
    
    // Test 1: Requirement Creation Performance (BEFORE vs AFTER)
    console.log('📊 TEST 1: Requirement Creation Performance');
    console.log('   (With optimized async timeline + skip validation + batch events)\n');
    
    const creationTimes = [];
    
    for (let i = 0; i < 3; i++) {
      const start = Date.now();
      
      const response = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${token}`)
        .send({
          project: project.elementId,
          title: `Optimized Test Requirement ${i + 1}`,
          description: 'Testing optimized timeline performance impact',
          type: 'requirement'
        });
      
      const duration = Date.now() - start;
      creationTimes.push(duration);
      
      console.log(`   Creation ${i + 1}: ${duration}ms (Status: ${response.status})`);
    }
    
    const avgCreation = creationTimes.reduce((a, b) => a + b, 0) / creationTimes.length;
    console.log(`   Average creation time: ${avgCreation.toFixed(2)}ms\n`);
    
    // Test 2: Requirement Update Performance (BEFORE vs AFTER)
    console.log('📊 TEST 2: Requirement Update Performance');
    console.log('   (With batch timeline events + skip validation)\n');
    
    // Find a requirement to update
    const Requirement = require('../../models/Requirement');
    const testReq = await Requirement.findOne({});
    
    if (testReq) {
      const updateTimes = [];
      
      for (let i = 0; i < 3; i++) {
        const updateStart = Date.now();
        
        const updateResponse = await request(app)
          .put(`/api/requirements/${testReq._id}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            title: `Optimized Update Test ${i + 1}`,
            description: `Updated description ${i + 1} to test optimized timeline performance`
          });
        
        const updateDuration = Date.now() - updateStart;
        updateTimes.push(updateDuration);
        
        console.log(`   Update ${i + 1}: ${updateDuration}ms (Status: ${updateResponse.status})`);
      }
      
      const avgUpdate = updateTimes.reduce((a, b) => a + b, 0) / updateTimes.length;
      console.log(`   Average update time: ${avgUpdate.toFixed(2)}ms\n`);
      
      // Test 3: State Change Performance
      console.log('📊 TEST 3: State Change Performance');
      console.log('   (With optimized async timeline + skip validation)\n');
      
      const stateTimes = [];
      
      for (let i = 0; i < 3; i++) {
        const stateStart = Date.now();
        
        const stateResponse = await request(app)
          .post(`/api/requirements/${testReq._id}/transition-state`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            newState: i % 2 === 0 ? 'Being Drafted' : 'New',
            version: 1
          });
        
        const stateDuration = Date.now() - stateStart;
        stateTimes.push(stateDuration);
        
        console.log(`   State change ${i + 1}: ${stateDuration}ms (Status: ${stateResponse.status})`);
      }
      
      const avgState = stateTimes.reduce((a, b) => a + b, 0) / stateTimes.length;
      console.log(`   Average state change time: ${avgState.toFixed(2)}ms\n`);
      
      // Performance Analysis
      console.log('🎯 OPTIMIZED PERFORMANCE ANALYSIS:\n');
      
      console.log('📈 Requirement Creation:');
      console.log(`   BEFORE: ~300ms | AFTER: ${avgCreation.toFixed(2)}ms`);
      if (avgCreation < 200) {
        console.log('   ✅ IMPROVED: Significant performance improvement!');
      } else if (avgCreation < 250) {
        console.log('   ⚡ BETTER: Moderate improvement, still room for optimization');
      } else {
        console.log('   ⚠️  LIMITED: Minimal improvement, other bottlenecks remain');
      }
      
      console.log('\\n📈 Requirement Update:');
      console.log(`   BEFORE: ~674ms | AFTER: ${avgUpdate.toFixed(2)}ms`);
      if (avgUpdate < 300) {
        console.log('   ✅ MAJOR IMPROVEMENT: Batch timeline events working!');
      } else if (avgUpdate < 500) {
        console.log('   ⚡ GOOD IMPROVEMENT: Significant optimization achieved');
      } else {
        console.log('   ⚠️  SOME IMPROVEMENT: Partial optimization, more work needed');
      }
      
      console.log('\\n📈 State Change:');
      console.log(`   BEFORE: ~665ms | AFTER: ${avgState.toFixed(2)}ms`);
      if (avgState < 200) {
        console.log('   ✅ EXCELLENT: Major performance improvement!');
      } else if (avgState < 400) {
        console.log('   ⚡ GOOD: Significant improvement achieved');
      } else {
        console.log('   ⚠️  MODERATE: Some improvement, more optimization needed');
      }
      
      console.log('\\n🎯 OVERALL OPTIMIZATION ASSESSMENT:');
      const totalBefore = 300 + 674 + 665; // Previous measurements
      const totalAfter = avgCreation + avgUpdate + avgState;
      const improvement = ((totalBefore - totalAfter) / totalBefore * 100).toFixed(1);
      
      console.log(`   BEFORE: ${totalBefore}ms total`);
      console.log(`   AFTER:  ${totalAfter.toFixed(2)}ms total`);
      console.log(`   IMPROVEMENT: ${improvement}% faster`);
      
      if (improvement > 50) {
        console.log('   🎉 EXCELLENT: Major performance optimization achieved!');
        console.log('   ✅ Timeline system optimizations are highly effective');
      } else if (improvement > 25) {
        console.log('   ⚡ GOOD: Significant performance improvement');
        console.log('   ✅ Timeline optimizations are working well');
      } else if (improvement > 10) {
        console.log('   ⚡ MODERATE: Noticeable performance improvement');
        console.log('   ⚠️  Some timeline bottlenecks remain');
      } else {
        console.log('   ⚠️  LIMITED: Minimal performance improvement');
        console.log('   🔍 Other bottlenecks may be the primary issue');
      }
      
      // Wait for async timeline events to complete
      console.log('\\n⏳ Waiting for optimized async timeline events to complete...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check if timeline events were created efficiently
      const TimelineEvent = require('../../models/TimelineEvent');
      const recentEvents = await TimelineEvent.find({
        requirementId: testReq._id
      }).sort({ timestamp: -1 }).limit(10);
      
      console.log(`✅ Timeline events created: ${recentEvents.length} recent events found`);
      console.log('   Optimized async timeline creation is working correctly!');
      
      // Final recommendation
      console.log('\\n💡 OPTIMIZATION RECOMMENDATIONS:');
      
      if (avgUpdate > 300) {
        console.log('   🔧 Requirement updates still slow - consider:');
        console.log('      • Background job processing for timeline events');
        console.log('      • Further database query optimization');
        console.log('      • Caching frequently accessed data');
      }
      
      if (avgCreation > 200) {
        console.log('   🔧 Requirement creation still slow - likely due to:');
        console.log('      • Project/feature resolution logic');
        console.log('      • Complex validation processes');
        console.log('      • Database population queries');
      }
      
      console.log('\\n✅ Timeline system optimizations implemented:');
      console.log('   • Skip validation for trusted contexts');
      console.log('   • Batch timeline event creation');
      console.log('   • Asynchronous timeline processing');
      console.log('   • Optimized change detection logic');
      
    } else {
      console.log('❌ No requirement found for update/state change tests');
    }
    
    await mongoose.disconnect();
    console.log('\\n🎉 Optimized timeline performance test completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
    await mongoose.disconnect();
  }
}

// Run the test
testOptimizedTimelinePerformance();

const request = require('supertest');
const app = require('../../app');
const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
require('dotenv').config();

async function testAsyncTimelinePerformance() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('🚀 TESTING ASYNC TIMELINE PERFORMANCE\n');
    
    // Find existing data
    const User = require('../../models/User');
    const Group = require('../../models/Group');
    const Project = require('../../models/Project');
    
    const user = await User.findOne({});
    const group = await Group.findOne({});
    const project = await Project.findOne({});
    
    if (!user || !group || !project) {
      console.log('❌ No test data found');
      await mongoose.disconnect();
      return;
    }
    
    console.log('✅ Test data found:');
    console.log(`   User: ${user.username}`);
    console.log(`   Group: ${group.name}`);
    console.log(`   Project: ${project.name}\n`);
    
    // Create a test token
    const token = jwt.sign(
      { userId: user._id, groupId: group._id },
      process.env.JWT_SECRET || 'test-secret'
    );
    
    // Test 1: Requirement Creation Performance
    console.log('📊 TEST 1: Requirement Creation (with async timeline)');
    const creationTimes = [];
    
    for (let i = 0; i < 3; i++) {
      const start = Date.now();
      
      const response = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${token}`)
        .send({
          project: project.elementId,
          title: `Async Test Requirement ${i + 1}`,
          description: 'Testing async timeline performance impact',
          type: 'requirement'
        });
      
      const duration = Date.now() - start;
      creationTimes.push(duration);
      
      console.log(`   Creation ${i + 1}: ${duration}ms (Status: ${response.status})`);
    }
    
    const avgCreation = creationTimes.reduce((a, b) => a + b, 0) / creationTimes.length;
    console.log(`   Average creation time: ${avgCreation.toFixed(2)}ms\n`);
    
    // Test 2: Requirement Update Performance
    console.log('📊 TEST 2: Requirement Update (with async timeline)');
    
    // Find a requirement to update
    const Requirement = require('../../models/Requirement');
    const testReq = await Requirement.findOne({});
    
    if (testReq) {
      const updateStart = Date.now();
      
      const updateResponse = await request(app)
        .put(`/api/requirements/${testReq._id}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          title: 'Updated Title for Async Test',
          description: 'Updated description to test async timeline performance'
        });
      
      const updateDuration = Date.now() - updateStart;
      console.log(`   Update time: ${updateDuration}ms (Status: ${updateResponse.status})\n`);
      
      // Test 3: State Change Performance
      console.log('📊 TEST 3: State Change (with async timeline)');
      
      const stateStart = Date.now();
      
      const stateResponse = await request(app)
        .post(`/api/requirements/${testReq._id}/transition-state`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          newState: 'Being Drafted',
          version: 1
        });
      
      const stateDuration = Date.now() - stateStart;
      console.log(`   State change time: ${stateDuration}ms (Status: ${stateResponse.status})\n`);
      
      // Performance Analysis
      console.log('🎯 PERFORMANCE ANALYSIS:\n');
      
      console.log('📈 Requirement Creation:');
      if (avgCreation > 200) {
        console.log('   ⚠️  Still slow (>200ms) - may need further optimization');
      } else if (avgCreation > 100) {
        console.log('   ⚡ Improved but moderate (100-200ms)');
      } else {
        console.log('   ✅ Fast (<100ms) - async timeline is working well!');
      }
      
      console.log('📈 Requirement Update:');
      if (updateDuration > 300) {
        console.log('   ⚠️  Still slow (>300ms) - multiple async events may need batching');
      } else if (updateDuration > 150) {
        console.log('   ⚡ Improved but moderate (150-300ms)');
      } else {
        console.log('   ✅ Fast (<150ms) - async timeline is working well!');
      }
      
      console.log('📈 State Change:');
      if (stateDuration > 200) {
        console.log('   ⚠️  Still slow (>200ms) - may need optimization');
      } else if (stateDuration > 100) {
        console.log('   ⚡ Improved but moderate (100-200ms)');
      } else {
        console.log('   ✅ Fast (<100ms) - async timeline is working well!');
      }
      
      console.log('\\n🎯 OVERALL ASSESSMENT:');
      const totalTime = avgCreation + updateDuration + stateDuration;
      
      if (totalTime > 600) {
        console.log('⚠️  STILL NEEDS WORK: Total operation time is high');
        console.log('   Consider batching timeline events or background processing');
      } else if (totalTime > 300) {
        console.log('⚡ IMPROVED: Significant performance improvement');
        console.log('   Async timeline creation is helping, monitor user feedback');
      } else {
        console.log('✅ EXCELLENT: Major performance improvement achieved');
        console.log('   Async timeline creation has resolved the performance issues');
      }
      
      // Wait a moment for async timeline events to complete
      console.log('\\n⏳ Waiting for async timeline events to complete...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check if timeline events were created
      const TimelineEvent = require('../../models/TimelineEvent');
      const recentEvents = await TimelineEvent.find({
        requirementId: testReq._id
      }).sort({ timestamp: -1 }).limit(5);
      
      console.log(`✅ Timeline events created: ${recentEvents.length} recent events found`);
      console.log('   Async timeline creation is working correctly!');
      
    } else {
      console.log('❌ No requirement found for update/state change tests');
    }
    
    await mongoose.disconnect();
    console.log('\\n🎉 Async timeline performance test completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
    await mongoose.disconnect();
  }
}

// Run the test
testAsyncTimelinePerformance();

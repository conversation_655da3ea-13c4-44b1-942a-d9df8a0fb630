const request = require('supertest');
const app = require('../../app');
const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
require('dotenv').config();

async function testLinkedTextsEndpoint() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('🔍 TESTING LINKED-TEXTS ENDPOINT (UI ERROR REPRODUCTION)');
    
    // Find existing data
    const User = require('../../models/User');
    const Group = require('../../models/Group');
    const Feature = require('../../models/Feature');
    
    const user = await User.findOne({});
    const group = await Group.findOne({});
    const feature = await Feature.findOne({});
    
    if (!user || !group || !feature) {
      console.log('❌ No test data found');
      await mongoose.disconnect();
      return;
    }
    
    console.log('✅ Test data found:');
    console.log(`   User: ${user.username}`);
    console.log(`   Group: ${group.name}`);
    console.log(`   Feature ElementID: ${feature.elementId}`);
    console.log(`   Feature ObjectID: ${feature._id}`);
    console.log(`   Feature Group: ${feature.group}`);
    console.log(`   User Group: ${group._id}\n`);
    
    // Check if feature belongs to user's group
    const groupMatch = feature.group.toString() === group._id.toString();
    console.log(`   Group Match: ${groupMatch}\n`);
    
    // Create a test token
    const token = jwt.sign(
      { userId: user._id, groupId: group._id },
      process.env.JWT_SECRET || 'test-secret'
    );
    
    // Test the exact endpoint that's failing in the UI
    console.log('📊 Testing linked-texts endpoint (EXACT UI CALL)...');
    console.log(`   URL: /api/features/${feature.elementId}/linked-texts?version=1`);
    
    const response = await request(app)
      .get(`/api/features/${feature.elementId}/linked-texts?version=1`)
      .set('Authorization', `Bearer ${token}`);
    
    console.log(`   Response status: ${response.status}`);
    console.log(`   Response body:`, response.body);
    
    if (response.status === 400) {
      console.log('\n❌ 400 Bad Request - This matches the UI error!');
      console.log('   Possible causes:');
      console.log('   1. ElementID validation failing');
      console.log('   2. Feature not found in user\'s group');
      console.log('   3. Invalid version parameter');
      console.log('   4. Middleware authentication issue');
    } else if (response.status === 200) {
      console.log('\n✅ 200 OK - Endpoint working correctly');
    } else {
      console.log(`\n⚠️  Unexpected status: ${response.status}`);
    }
    
    // Test with ObjectID as well
    console.log('\n📊 Testing with ObjectID instead of ElementID...');
    console.log(`   URL: /api/features/${feature._id}/linked-texts?version=1`);
    
    const objectIdResponse = await request(app)
      .get(`/api/features/${feature._id}/linked-texts?version=1`)
      .set('Authorization', `Bearer ${token}`);
    
    console.log(`   ObjectID Response status: ${objectIdResponse.status}`);
    console.log(`   ObjectID Response body:`, objectIdResponse.body);
    
    // Test without version parameter
    console.log('\n📊 Testing without version parameter...');
    console.log(`   URL: /api/features/${feature.elementId}/linked-texts`);
    
    const noVersionResponse = await request(app)
      .get(`/api/features/${feature.elementId}/linked-texts`)
      .set('Authorization', `Bearer ${token}`);
    
    console.log(`   No version Response status: ${noVersionResponse.status}`);
    console.log(`   No version Response body:`, noVersionResponse.body);
    
    // Test ElementID validation directly
    console.log('\n📊 Testing ElementID validation...');
    const { validateElementId } = require('../../utils/elementIdGenerator');
    const isValidElementId = validateElementId(feature.elementId);
    console.log(`   ElementID "${feature.elementId}" is valid: ${isValidElementId}`);
    
    // Test if feature exists in database with group filter
    console.log('\n📊 Testing database lookup...');
    const dbFeature = await Feature.findOne({
      elementId: feature.elementId,
      group: group._id
    });
    console.log(`   Feature found in DB with group filter: ${!!dbFeature}`);
    
    if (!dbFeature) {
      console.log('   ❌ This is the issue! Feature not found with group filter');
      console.log('   The ElementID resolver is failing because of group mismatch');
    }
    
    // Check what happens without group filter
    const dbFeatureNoGroup = await Feature.findOne({
      elementId: feature.elementId
    });
    console.log(`   Feature found in DB without group filter: ${!!dbFeatureNoGroup}`);
    
    if (dbFeatureNoGroup) {
      console.log(`   Feature actual group: ${dbFeatureNoGroup.group}`);
      console.log(`   User group: ${group._id}`);
      console.log(`   Groups match: ${dbFeatureNoGroup.group.toString() === group._id.toString()}`);
    }
    
    // Final diagnosis
    console.log('\n🎯 DIAGNOSIS:');
    if (!groupMatch) {
      console.log('   ❌ ROOT CAUSE: Feature belongs to different group than user');
      console.log('   ❌ This is a multi-tenancy issue, not a timeline issue');
      console.log('   💡 SOLUTION: Ensure test data has matching groups');
    } else if (!isValidElementId) {
      console.log('   ❌ ROOT CAUSE: Invalid ElementID format');
      console.log('   💡 SOLUTION: Fix ElementID validation logic');
    } else if (response.status === 400) {
      console.log('   ❌ ROOT CAUSE: Unknown validation issue in middleware');
      console.log('   💡 SOLUTION: Check elementIdResolver middleware logs');
    } else {
      console.log('   ✅ No obvious issues found - may be intermittent');
    }
    
    await mongoose.disconnect();
    console.log('\n🎉 Linked-texts endpoint test completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
    await mongoose.disconnect();
  }
}

// Run the test
testLinkedTextsEndpoint();

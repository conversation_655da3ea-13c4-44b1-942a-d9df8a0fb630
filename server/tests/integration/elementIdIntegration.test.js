const request = require('supertest');
const app = require('../../app');
const { createTestSetup } = require('../helpers/testHelpers');
const Project = require('../../models/Project');
const Feature = require('../../models/Feature');
const Requirement = require('../../models/Requirement');

describe('ElementID Integration Tests - End-to-End Workflows', () => {
  let testSetup;
  let createdProject;
  let createdFeature;
  let createdRequirement;

  beforeAll(async () => {
    testSetup = await createTestSetup();
  });

  describe('Complete CRUD Workflow with ElementIDs', () => {
    test('should create project and generate ElementID', async () => {
      const projectData = {
        name: 'Integration Test Project',
        description: 'Project for testing ElementID integration'
      };

      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send(projectData);

      expect(response.status).toBe(200);
      expect(response.body.elementId).toMatch(/^[A-Z]{2,4}-\d{3,}$/);
      
      createdProject = response.body;
    });

    test('should create feature under project using ElementID', async () => {
      const featureData = {
        project: createdProject.elementId, // Use ElementID instead of ObjectID
        title: 'Integration Test Feature',
        description: 'Feature for testing ElementID integration'
      };

      const response = await request(app)
        .post('/api/features')
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send(featureData);

      expect(response.status).toBe(200);
      expect(response.body.elementId).toMatch(/^[A-Z]{2,4}-F-\d{3,}$/);
      expect(response.body.project).toBe(createdProject._id);
      
      createdFeature = response.body;
    });

    test('should create requirement under feature using ElementID', async () => {
      const requirementData = {
        project: createdProject.elementId, // Use ElementID
        feature: createdFeature.elementId, // Use ElementID
        title: 'Integration Test Requirement',
        description: 'Requirement for testing ElementID integration',
        type: 'requirement'
      };

      const response = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send(requirementData);

      expect(response.status).toBe(200);
      expect(response.body.elementId).toMatch(/^[A-Z]{2,4}-R-\d{3,}$/);
      expect(response.body.project).toBe(createdProject._id);
      expect(response.body.feature).toBe(createdFeature._id);
      
      createdRequirement = response.body;
    });

    test('should retrieve all created items using ElementIDs', async () => {
      // Get project by ElementID
      const projectResponse = await request(app)
        .get(`/api/projects/${createdProject.elementId}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);
      
      expect(projectResponse.status).toBe(200);
      expect(projectResponse.body._id).toBe(createdProject._id);

      // Get feature by ElementID
      const featureResponse = await request(app)
        .get(`/api/features/${createdFeature.elementId}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);
      
      expect(featureResponse.status).toBe(200);
      expect(featureResponse.body._id).toBe(createdFeature._id);

      // Get requirement by ElementID
      const requirementResponse = await request(app)
        .get(`/api/requirements/${createdRequirement.elementId}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);
      
      expect(requirementResponse.status).toBe(200);
      expect(requirementResponse.body._id).toBe(createdRequirement._id);
    });

    test('should update items using ElementIDs', async () => {
      // Update project using ElementID
      const projectUpdate = {
        name: 'Updated Integration Test Project',
        description: 'Updated description'
      };

      const projectResponse = await request(app)
        .put(`/api/projects/${createdProject.elementId}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send(projectUpdate);

      expect(projectResponse.status).toBe(200);
      expect(projectResponse.body.name).toBe(projectUpdate.name);

      // Update requirement using ElementID
      const requirementUpdate = {
        title: 'Updated Integration Test Requirement',
        description: 'Updated requirement description'
      };

      const requirementResponse = await request(app)
        .put(`/api/requirements/${createdRequirement.elementId}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send(requirementUpdate);

      expect(requirementResponse.status).toBe(200);
      expect(requirementResponse.body.versions[0].title).toBe(requirementUpdate.title);
    });

    test('should handle navigation and relationships with ElementIDs', async () => {
      // Get feature children using ElementID
      const childrenResponse = await request(app)
        .get(`/api/requirements/${createdFeature.elementId}/children`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(childrenResponse.status).toBe(200);
      expect(Array.isArray(childrenResponse.body)).toBe(true);
      expect(childrenResponse.body.length).toBeGreaterThan(0);
      
      const foundRequirement = childrenResponse.body.find(
        req => req.elementId === createdRequirement.elementId
      );
      expect(foundRequirement).toBeDefined();
    });
  });

  describe('Mixed ObjectID/ElementID Operations', () => {
    test('should handle mixed ID types in same request', async () => {
      // Create requirement using ObjectID for project, ElementID for feature
      const mixedRequirementData = {
        project: createdProject._id, // ObjectID
        feature: createdFeature.elementId, // ElementID
        title: 'Mixed ID Test Requirement',
        description: 'Testing mixed ObjectID and ElementID usage',
        type: 'requirement'
      };

      const response = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send(mixedRequirementData);

      expect(response.status).toBe(200);
      expect(response.body.project).toBe(createdProject._id);
      expect(response.body.feature).toBe(createdFeature._id);
    });

    test('should maintain consistency across ID types', async () => {
      // Fetch same item using both ID types
      const objectIdResponse = await request(app)
        .get(`/api/features/${createdFeature._id}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      const elementIdResponse = await request(app)
        .get(`/api/features/${createdFeature.elementId}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(objectIdResponse.status).toBe(200);
      expect(elementIdResponse.status).toBe(200);
      expect(objectIdResponse.body._id).toBe(elementIdResponse.body._id);
      expect(objectIdResponse.body.elementId).toBe(elementIdResponse.body.elementId);
    });
  });

  describe('Error Scenarios and Edge Cases', () => {
    test('should handle ElementID not found gracefully', async () => {
      const response = await request(app)
        .get('/api/features/NONEXIST-F-999')
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.message).toContain('not found');
    });

    test('should handle malformed ElementIDs', async () => {
      const malformedIds = [
        'invalid-format',
        'TEST-X-001', // Invalid type
        'TOO-LONG-ACRONYM-F-001',
        'A-F-001' // Too short acronym
      ];

      for (const id of malformedIds) {
        const response = await request(app)
          .get(`/api/features/${id}`)
          .set('Authorization', `Bearer ${testSetup.adminToken}`);

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('Invalid');
      }
    });

    test('should respect multi-tenancy with ElementIDs', async () => {
      // This would test that ElementIDs are properly scoped to groups
      // and users can't access ElementIDs from other groups
      const response = await request(app)
        .get(`/api/features/${createdFeature.elementId}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.group).toBe(testSetup.group._id.toString());
    });
  });

  describe('Performance and Consistency', () => {
    test('should perform similarly with ObjectID vs ElementID', async () => {
      const startObjectId = Date.now();
      await request(app)
        .get(`/api/features/${createdFeature._id}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);
      const objectIdTime = Date.now() - startObjectId;

      const startElementId = Date.now();
      await request(app)
        .get(`/api/features/${createdFeature.elementId}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);
      const elementIdTime = Date.now() - startElementId;

      // ElementID lookup should not be significantly slower
      // Allow 5x slower as reasonable threshold for additional lookup
      expect(elementIdTime).toBeLessThan(objectIdTime * 5);
    });
  });
});

const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../../app');
const { createTestSetup } = require('../helpers/testHelpers');
const TimelineEvent = require('../../models/TimelineEvent');
const ArchivedTimelineEvent = require('../../models/ArchivedTimelineEvent');
const Group = require('../../models/Group');
const TimelineService = require('../../services/TimelineService');
const ArchiveService = require('../../services/ArchiveService');
const { createTestDataWithTimeline } = require('./timelineSystem.test');

describe('Archive System Integration Tests', () => {
  let testSetup;

  beforeAll(async () => {
    testSetup = await createTestSetup();
  });

  afterAll(async () => {
    // Cleanup will be handled by Jest teardown
  });

  beforeEach(async () => {
    // Clean timeline and archive events before each test
    await TimelineEvent.deleteMany({});
    await ArchivedTimelineEvent.deleteMany({});
  });

  describe('Group Retention Policies', () => {
    test('should have default retention policy for new groups', async () => {
      const group = await Group.findById(testSetup.group._id);
      
      expect(group.retentionPolicy).toBeDefined();
      expect(group.retentionPolicy.tier).toBeDefined();
      expect(group.retentionPolicy.retentionMonths).toBeGreaterThan(0);
      expect(Array.isArray(group.retentionPolicy.expirationWarningDays)).toBe(true);
      expect(group.retentionPolicy.archivalEnabled).toBe(false); // Default disabled
    });

    test('should calculate retention policy based on group size', async () => {
      // Test small group (1-3 users)
      const { createTestGroup } = require('../helpers/testHelpers');
      const smallGroup = await createTestGroup('small-group', 'Small Group', '1-3', testSetup.superUser);
      expect(smallGroup.getRetentionPolicy().retentionMonths).toBe(12);

      // Test medium group (4-25 users)
      const mediumGroup = await createTestGroup('medium-group', 'Medium Group', '4-25', testSetup.superUser);
      expect(mediumGroup.getRetentionPolicy().retentionMonths).toBe(24);

      // Test large group (26-100 users)
      const largeGroup = await createTestGroup('large-group', 'Large Group', '26-100', testSetup.superUser);
      expect(largeGroup.getRetentionPolicy().retentionMonths).toBe(36);

      // Test enterprise group (100+ users)
      const enterpriseGroup = await createTestGroup('enterprise-group', 'Enterprise Group', '100+', testSetup.superUser);
      expect(enterpriseGroup.getRetentionPolicy().retentionMonths).toBe(60);
    });

    test('should check retention warnings correctly', async () => {
      const group = await Group.findById(testSetup.group._id);
      
      const warningCheck = group.needsRetentionWarning();
      expect(warningCheck).toBeDefined();
      expect(typeof warningCheck.needsWarning).toBe('boolean');
      expect(typeof warningCheck.daysUntilExpiration).toBe('number');
    });

    test('should calculate timeline expiration date', async () => {
      const group = await Group.findById(testSetup.group._id);
      
      const expirationDate = group.getTimelineExpirationDate();
      expect(expirationDate).toBeInstanceOf(Date);
      expect(expirationDate.getTime()).toBeLessThan(Date.now());
    });
  });

  describe('Timeline Archival', () => {
    test('should archive old timeline events', async () => {
      // Create test data with timeline events
      const { requirement } = await createTestDataWithTimeline();

      // Manually create old timeline events (simulate old data)
      const oldDate = new Date();
      oldDate.setFullYear(oldDate.getFullYear() - 2); // 2 years ago

      const oldEvent = new TimelineEvent({
        requirementId: requirement._id,
        groupId: testSetup.group._id,
        eventType: 'state_changed',
        user: testSetup.adminUser._id,
        version: 1,
        eventData: { from: 'New', to: 'Being Drafted' },
        description: 'Old state change event',
        timestamp: oldDate
      });

      await oldEvent.save();

      // Enable archival for the group
      const group = await Group.findById(testSetup.group._id);
      group.retentionPolicy.archivalEnabled = true;
      group.retentionPolicy.retentionMonths = 12; // 1 year retention
      await group.save();

      // Run archival
      const result = await TimelineService.archiveOldEvents(testSetup.group._id);

      expect(result.archived).toBeGreaterThan(0);
      expect(result.message).toContain('archived');

      // Verify event was archived
      const archivedEvents = await ArchivedTimelineEvent.find({
        groupId: testSetup.group._id
      });

      expect(archivedEvents.length).toBeGreaterThan(0);

      const archivedEvent = archivedEvents.find(
        event => event.originalEventId.toString() === oldEvent._id.toString()
      );

      expect(archivedEvent).toBeDefined();
      expect(archivedEvent.eventData.eventType).toBe('state_changed');
      expect(archivedEvent.retentionTier).toBe(group.retentionPolicy.tier);

      // Verify original event was deleted
      const remainingEvent = await TimelineEvent.findById(oldEvent._id);
      expect(remainingEvent).toBeNull();
    });

    test('should not archive when archival is disabled', async () => {
      const { requirement } = await createTestDataWithTimeline();

      // Create old event
      const oldDate = new Date();
      oldDate.setFullYear(oldDate.getFullYear() - 2);

      const oldEvent = new TimelineEvent({
        requirementId: requirement._id,
        groupId: testSetup.group._id,
        eventType: 'created',
        user: testSetup.adminUser._id,
        version: 1,
        eventData: { title: 'Test' },
        description: 'Old creation event',
        timestamp: oldDate
      });

      await oldEvent.save();

      // Ensure archival is disabled
      const group = await Group.findById(testSetup.group._id);
      group.retentionPolicy.archivalEnabled = false;
      await group.save();

      // Try to run archival
      const result = await TimelineService.archiveOldEvents(testSetup.group._id);

      expect(result.archived).toBe(0);
      expect(result.message).toContain('disabled');

      // Verify event was not archived
      const remainingEvent = await TimelineEvent.findById(oldEvent._id);
      expect(remainingEvent).toBeDefined();
    });

    test('should restore archived events', async () => {
      // Create and archive an event
      const { requirement } = await createTestDataWithTimeline();

      const oldEvent = new TimelineEvent({
        requirementId: requirement._id,
        groupId: testSetup.group._id,
        eventType: 'state_changed',
        user: testSetup.adminUser._id,
        version: 1,
        eventData: { from: 'New', to: 'Being Drafted' },
        description: 'Event to be archived and restored',
        timestamp: new Date(Date.now() - 2 * 365 * 24 * 60 * 60 * 1000) // 2 years ago
      });

      await oldEvent.save();

      // Enable archival and run it
      const group = await Group.findById(testSetup.group._id);
      group.retentionPolicy.archivalEnabled = true;
      group.retentionPolicy.retentionMonths = 12;
      await group.save();

      await TimelineService.archiveOldEvents(testSetup.group._id);

      // Verify event was archived
      const archivedEvent = await ArchivedTimelineEvent.findOne({
        originalEventId: oldEvent._id
      });
      expect(archivedEvent).toBeDefined();

      // Restore the archived event
      const restoreResult = await ArchiveService.restoreArchivedEvents(
        requirement._id,
        testSetup.group._id
      );

      expect(restoreResult.restored).toBe(1);

      // Verify event was restored
      const restoredEvents = await TimelineEvent.find({
        requirementId: requirement._id,
        eventType: 'state_changed'
      });

      expect(restoredEvents.length).toBeGreaterThan(0);

      // Verify archived event was removed
      const remainingArchived = await ArchivedTimelineEvent.findById(archivedEvent._id);
      expect(remainingArchived).toBeNull();
    });
  });

  describe('Archive Service', () => {
    test('should get archival statistics', async () => {
      // Create some archived events
      const archivedEvent = new ArchivedTimelineEvent({
        originalEventId: new mongoose.Types.ObjectId(),
        originalRequirementId: new mongoose.Types.ObjectId(),
        groupId: testSetup.group._id,
        retentionTier: 'small',
        eventData: {
          eventType: 'created',
          timestamp: new Date(),
          user: testSetup.adminUser._id,
          description: 'Test archived event'
        }
      });

      await archivedEvent.save();

      const stats = await ArchiveService.getArchivalStats();

      expect(stats.totalArchived).toBeGreaterThan(0);
      expect(Array.isArray(stats.byTier)).toBe(true);
      expect(stats.byTier.length).toBeGreaterThan(0);

      const smallTierStats = stats.byTier.find(tier => tier._id === 'small');
      expect(smallTierStats).toBeDefined();
      expect(smallTierStats.count).toBeGreaterThan(0);
    });

    test('should get retention recommendations', async () => {
      const { requirement } = await createTestDataWithTimeline();

      // Add some timeline activity
      await request(app)
        .post(`/api/requirements/${requirement._id}/transition-state`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({ newState: 'Being Drafted', version: 1 });

      const recommendations = await ArchiveService.getRetentionRecommendations(
        testSetup.group._id
      );

      expect(recommendations.recommendation).toBeDefined();
      expect(recommendations.recommendation.currentTier).toBeDefined();
      expect(recommendations.recommendation.currentRetentionMonths).toBeGreaterThan(0);
      expect(recommendations.recommendation.usagePattern).toBeDefined();
      expect(Array.isArray(recommendations.usageStats)).toBe(true);
      expect(typeof recommendations.totalEvents).toBe('number');
    });

    test('should create mock purchase extension', async () => {
      const purchase = await ArchiveService.mockPurchaseMoreTime(
        testSetup.group._id,
        2 // 2 year extension
      );

      expect(purchase.success).toBe(true);
      expect(purchase.extensionYears).toBe(2);
      expect(purchase.price).toBe(179); // 2 year price
      expect(purchase.orderId).toContain('ORDER-');
      expect(purchase.paymentUrl).toContain('/admin/payment/confirm');
      expect(purchase.newExpirationDate).toBeInstanceOf(Date);
    });

    test('should validate purchase extension parameters', async () => {
      // Test invalid extension period
      await expect(
        ArchiveService.mockPurchaseMoreTime(testSetup.group._id, 5)
      ).rejects.toThrow('Invalid extension period');

      // Test invalid group
      await expect(
        ArchiveService.mockPurchaseMoreTime(new mongoose.Types.ObjectId(), 1)
      ).rejects.toThrow('Group not found');
    });

    test('should check all groups for archival needs', async () => {
      // Enable archival for test group
      const group = await Group.findById(testSetup.group._id);
      group.retentionPolicy.archivalEnabled = true;
      await group.save();

      const results = await ArchiveService.checkAllGroups();

      expect(results.warnings).toBeDefined();
      expect(results.archival).toBeDefined();
      expect(results.errors).toBeDefined();
      expect(Array.isArray(results.warnings)).toBe(true);
      expect(Array.isArray(results.archival)).toBe(true);
      expect(Array.isArray(results.errors)).toBe(true);
    });
  });

  describe('Archive API Routes', () => {
    test('should get archival stats via API', async () => {
      const response = await request(app)
        .get('/api/timeline/admin/archival-stats')
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.totalArchived).toBeDefined();
      expect(Array.isArray(response.body.byTier)).toBe(true);
    });

    test('should get retention warnings via API', async () => {
      const response = await request(app)
        .get('/api/timeline/admin/retention-warnings')
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.warning).toBeDefined();
      expect(response.body.recommendations).toBeDefined();
      expect(response.body.currentPolicy).toBeDefined();
    });

    test('should create purchase extension via API', async () => {
      const response = await request(app)
        .post('/api/timeline/admin/purchase-extension')
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({ extensionYears: 1 });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.extensionYears).toBe(1);
      expect(response.body.price).toBe(99);
    });

    test('should reject invalid purchase extension via API', async () => {
      const response = await request(app)
        .post('/api/timeline/admin/purchase-extension')
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({ extensionYears: 5 });

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Invalid extension period');
    });

    test('should require admin access for admin routes', async () => {
      // Create regular user
      const { createTestUser, generateAuthToken } = require('../helpers/testHelpers');
      const regularUser = await createTestUser({
        username: 'regular-user',
        email: '<EMAIL>',
        firstName: 'Regular',
        lastName: 'User',
        groupStatus: 'active'
      }, testSetup.group);
      const regularToken = generateAuthToken(regularUser);

      const response = await request(app)
        .get('/api/timeline/admin/archival-stats')
        .set('Authorization', `Bearer ${regularToken}`);

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Admin access required');
    });
  });

  describe('Data Integrity', () => {
    test('should maintain referential integrity during archival', async () => {
      const { requirement } = await createTestDataWithTimeline();

      // Create timeline event
      const event = new TimelineEvent({
        requirementId: requirement._id,
        groupId: testSetup.group._id,
        eventType: 'state_changed',
        user: testSetup.adminUser._id,
        version: 1,
        eventData: { from: 'New', to: 'Being Drafted' },
        description: 'Test event for integrity check',
        timestamp: new Date(Date.now() - 2 * 365 * 24 * 60 * 60 * 1000)
      });

      await event.save();

      // Enable archival
      const group = await Group.findById(testSetup.group._id);
      group.retentionPolicy.archivalEnabled = true;
      group.retentionPolicy.retentionMonths = 12;
      await group.save();

      // Archive the event
      await TimelineService.archiveOldEvents(testSetup.group._id);

      // Verify archived event maintains all original data
      const archivedEvent = await ArchivedTimelineEvent.findOne({
        originalEventId: event._id
      });

      expect(archivedEvent).toBeDefined();
      expect(archivedEvent.originalRequirementId.toString()).toBe(requirement._id);
      expect(archivedEvent.groupId.toString()).toBe(testSetup.group._id.toString());
      expect(archivedEvent.eventData.eventType).toBe('state_changed');
      expect(archivedEvent.eventData.user.toString()).toBe(testSetup.adminUser._id.toString());
      expect(archivedEvent.eventData.eventData.from).toBe('New');
      expect(archivedEvent.eventData.eventData.to).toBe('Being Drafted');
    });

    test('should handle archival transaction failures gracefully', async () => {
      const { requirement } = await createTestDataWithTimeline();

      // Create old event
      const oldEvent = new TimelineEvent({
        requirementId: requirement._id,
        groupId: testSetup.group._id,
        eventType: 'created',
        user: testSetup.adminUser._id,
        version: 1,
        eventData: { title: 'Test' },
        description: 'Event for transaction test',
        timestamp: new Date(Date.now() - 2 * 365 * 24 * 60 * 60 * 1000)
      });

      await oldEvent.save();

      // Enable archival
      const group = await Group.findById(testSetup.group._id);
      group.retentionPolicy.archivalEnabled = true;
      group.retentionPolicy.retentionMonths = 12;
      await group.save();

      // Mock a transaction failure by temporarily breaking the ArchivedTimelineEvent model
      const originalSave = ArchivedTimelineEvent.prototype.save;
      ArchivedTimelineEvent.prototype.save = function() {
        throw new Error('Simulated transaction failure');
      };

      try {
        await TimelineService.archiveOldEvents(testSetup.group._id);
      } catch (error) {
        expect(error.message).toContain('Simulated transaction failure');
      }

      // Restore the original save method
      ArchivedTimelineEvent.prototype.save = originalSave;

      // Verify original event still exists (transaction rolled back)
      const remainingEvent = await TimelineEvent.findById(oldEvent._id);
      expect(remainingEvent).toBeDefined();
    });
  });
});

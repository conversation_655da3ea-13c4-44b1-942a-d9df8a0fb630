const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../../app');
const { createTestSetup } = require('../helpers/testHelpers');
const TimelineEvent = require('../../models/TimelineEvent');
const Requirement = require('../../models/Requirement');
const TimelineService = require('../../services/TimelineService');
const ArchiveService = require('../../services/ArchiveService');

describe('Timeline System Core Tests', () => {
  let testSetup;

  beforeAll(async () => {
    testSetup = await createTestSetup();
  });

  beforeEach(async () => {
    // Clean timeline events before each test
    await TimelineEvent.deleteMany({});
  });

  describe('Timeline Service Core Functionality', () => {
    test('should create timeline events via service', async () => {
      // Create a requirement first
      const requirementResponse = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({
          project: testSetup.project.elementId,
          title: 'Test Requirement for Timeline',
          description: 'Testing timeline functionality',
          type: 'requirement'
        });

      const requirement = requirementResponse.body;

      // Get the requirement from database to get the ObjectId
      const dbRequirement = await Requirement.findOne({ elementId: requirement.elementId });

      // Create timeline event directly
      const event = await TimelineService.addEvent(
        dbRequirement._id,
        'created',
        testSetup.adminUser._id,
        testSetup.group._id,
        { title: requirement.title },
        1
      );

      expect(event).toBeDefined();
      expect(event.eventType).toBe('created');
      expect(event.user.toString()).toBe(testSetup.adminUser._id.toString());
      expect(event.description).toContain('created');
    });

    test('should get timeline events via service', async () => {
      // Create a requirement
      const requirementResponse = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({
          project: testSetup.project.elementId,
          title: 'Test Requirement',
          description: 'Testing timeline',
          type: 'requirement'
        });

      const requirement = requirementResponse.body;

      // Create timeline events
      await TimelineService.addEvent(
        requirement._id,
        'created',
        testSetup.adminUser._id,
        testSetup.group._id,
        { title: requirement.title },
        1
      );

      await TimelineService.addEvent(
        requirement._id,
        'state_changed',
        testSetup.adminUser._id,
        testSetup.group._id,
        { from: 'New', to: 'Being Drafted' },
        1
      );

      // Get timeline
      const timeline = await TimelineService.getRequirementTimeline(
        requirement._id,
        testSetup.group._id
      );

      expect(Array.isArray(timeline)).toBe(true);
      expect(timeline.length).toBe(2);
      
      const createdEvent = timeline.find(e => e.eventType === 'created');
      const stateEvent = timeline.find(e => e.eventType === 'state_changed');
      
      expect(createdEvent).toBeDefined();
      expect(stateEvent).toBeDefined();
    });

    test('should filter timeline by event type', async () => {
      // Create a requirement
      const requirementResponse = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({
          project: testSetup.project.elementId,
          title: 'Test Requirement',
          description: 'Testing timeline',
          type: 'requirement'
        });

      const requirement = requirementResponse.body;

      // Create multiple event types
      await TimelineService.addEvent(requirement._id, 'created', testSetup.adminUser._id, testSetup.group._id, {}, 1);
      await TimelineService.addEvent(requirement._id, 'state_changed', testSetup.adminUser._id, testSetup.group._id, {}, 1);
      await TimelineService.addEvent(requirement._id, 'comment_added', testSetup.adminUser._id, testSetup.group._id, {}, 1);

      // Filter by event type
      const stateTimeline = await TimelineService.getRequirementTimeline(
        requirement._id,
        testSetup.group._id,
        { eventTypes: ['state_changed'] }
      );

      expect(stateTimeline.length).toBe(1);
      expect(stateTimeline[0].eventType).toBe('state_changed');
    });

    test('should get timeline statistics', async () => {
      // Create a requirement
      const requirementResponse = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({
          project: testSetup.project.elementId,
          title: 'Test Requirement',
          description: 'Testing timeline',
          type: 'requirement'
        });

      const requirement = requirementResponse.body;

      // Create events
      await TimelineService.addEvent(requirement._id, 'created', testSetup.adminUser._id, testSetup.group._id, {}, 1);
      await TimelineService.addEvent(requirement._id, 'state_changed', testSetup.adminUser._id, testSetup.group._id, {}, 1);

      // Get stats
      const stats = await TimelineService.getTimelineStats(
        requirement._id,
        testSetup.group._id
      );

      expect(stats.totalEvents).toBe(2);
      expect(Array.isArray(stats.eventTypes)).toBe(true);
      expect(stats.eventTypes.length).toBeGreaterThan(0);
    });
  });

  describe('Timeline API Routes', () => {
    test('should get timeline via API', async () => {
      // Create a requirement
      const requirementResponse = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({
          project: testSetup.project.elementId,
          title: 'Test Requirement',
          description: 'Testing timeline',
          type: 'requirement'
        });

      const requirement = requirementResponse.body;

      // Create timeline event
      await TimelineService.addEvent(
        requirement._id,
        'created',
        testSetup.adminUser._id,
        testSetup.group._id,
        { title: requirement.title },
        1
      );

      // Get timeline via API
      const response = await request(app)
        .get(`/api/timeline/requirement/${requirement._id}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBe(1);
    });

    test('should get timeline stats via API', async () => {
      // Create a requirement
      const requirementResponse = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({
          project: testSetup.project.elementId,
          title: 'Test Requirement',
          description: 'Testing timeline',
          type: 'requirement'
        });

      const requirement = requirementResponse.body;

      // Create timeline event
      await TimelineService.addEvent(
        requirement._id,
        'created',
        testSetup.adminUser._id,
        testSetup.group._id,
        { title: requirement.title },
        1
      );

      // Get stats via API
      const response = await request(app)
        .get(`/api/timeline/requirement/${requirement._id}/stats`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.totalEvents).toBe(1);
      expect(Array.isArray(response.body.eventTypes)).toBe(true);
    });

    test('should get valid event types via API', async () => {
      const response = await request(app)
        .get('/api/timeline/event-types')
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body).toContain('created');
      expect(response.body).toContain('state_changed');
    });
  });

  describe('Multi-Tenancy', () => {
    test('should isolate timeline events by group', async () => {
      // Create requirements in different groups
      const req1Response = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({
          project: testSetup.project.elementId,
          title: 'Requirement 1',
          description: 'Testing',
          type: 'requirement'
        });

      const req1 = req1Response.body;

      // Create timeline events for different groups
      await TimelineService.addEvent(req1._id, 'created', testSetup.adminUser._id, testSetup.group._id, {}, 1);

      // Try to get timeline with wrong group ID
      const wrongGroupId = new mongoose.Types.ObjectId();
      const timeline = await TimelineService.getRequirementTimeline(req1._id, wrongGroupId);

      expect(timeline.length).toBe(0); // Should not find events from different group
    });
  });
});

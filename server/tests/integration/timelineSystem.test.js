const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../../app');
const { createTestSetup } = require('../helpers/testHelpers');
const TimelineEvent = require('../../models/TimelineEvent');
const TimelineService = require('../../services/TimelineService');
const ArchiveService = require('../../services/ArchiveService');

// Helper function to create complete test data with timeline events
const createTestDataWithTimeline = async (testSetup) => {
  // Create project
  const projectData = {
    name: 'Timeline Test Project',
    description: 'Project for testing timeline functionality'
  };

  const projectResponse = await request(app)
    .post('/api/projects')
    .set('Authorization', `Bearer ${testSetup.adminToken}`)
    .send(projectData);

  const project = projectResponse.body;

  // Create feature
  const featureData = {
    project: project.elementId,
    title: 'Timeline Test Feature',
    description: 'Feature for testing timeline functionality'
  };

  const featureResponse = await request(app)
    .post('/api/features')
    .set('Authorization', `Bearer ${testSetup.adminToken}`)
    .send(featureData);

  const feature = featureResponse.body;

  // Create requirement (should generate timeline events)
  const requirementData = {
    project: project.elementId,
    feature: feature.elementId,
    title: 'Timeline Test Requirement',
    description: 'Requirement for testing timeline functionality',
    type: 'requirement'
  };

  const requirementResponse = await request(app)
    .post('/api/requirements')
    .set('Authorization', `Bearer ${testSetup.adminToken}`)
    .send(requirementData);

  const requirement = requirementResponse.body;

  return { project, feature, requirement };
};

describe('Timeline System Integration Tests', () => {
  let testSetup;
  let testProject, testFeature, testRequirement;

  beforeAll(async () => {
    testSetup = await createTestSetup();
  });

  afterAll(async () => {
    // Cleanup will be handled by Jest teardown
  });

  // Helper function to wait for timeline events to be created
  const waitForTimelineEvents = async (requirementId, groupId, expectedCount = 1, maxWaitMs = 10000) => {
    const startTime = Date.now();
    while (Date.now() - startTime < maxWaitMs) {
      const events = await TimelineEvent.find({
        requirementId: requirementId,
        groupId: groupId
      });
      if (events.length >= expectedCount) {
        return events;
      }
      await new Promise(resolve => setTimeout(resolve, 200)); // Wait 200ms before retry
    }
    // Return whatever we found, even if less than expected
    return await TimelineEvent.find({
      requirementId: requirementId,
      groupId: groupId
    });
  };

  // Helper function to create timeline event directly (for testing)
  const createDirectTimelineEvent = async (requirementId, groupId, userId, eventType = 'created', eventData = {}) => {
    const event = await TimelineService.addEvent(
      requirementId,
      eventType,
      userId,
      groupId,
      eventData,
      1
    );
    return event;
  };

  describe('Timeline Event Creation', () => {
    test('should create timeline event directly via service', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      // Create timeline event directly
      const event = await createDirectTimelineEvent(
        requirement._id,
        testSetup.group._id,
        testSetup.adminUser._id,
        'created',
        { title: 'Test Requirement' }
      );

      expect(event).toBeDefined();
      expect(event.eventType).toBe('created');
      expect(event.user.toString()).toBe(testSetup.adminUser._id.toString());
      expect(event.description).toContain('created');
    });

    test('should create timeline event when requirement is created', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      // Wait for timeline events to be created
      const timeline = await waitForTimelineEvents(requirement._id, testSetup.group._id, 1);

      expect(timeline.length).toBeGreaterThan(0);

      const creationEvent = timeline.find(event => event.eventType === 'created');
      expect(creationEvent).toBeDefined();
      expect(creationEvent.user.toString()).toBe(testSetup.adminUser._id.toString());
      expect(creationEvent.version).toBe(1);
      expect(creationEvent.eventData.title).toBe('Timeline Test Requirement');
    });

    test('should create timeline event when requirement state changes', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      // Wait for initial creation event
      await waitForTimelineEvents(requirement._id, testSetup.group._id, 1);

      // Change state
      await request(app)
        .post(`/api/requirements/${requirement._id}/transition-state`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({
          newState: 'Being Drafted',
          version: 1
        });

      // Wait for state change event to be created
      await waitForTimelineEvents(requirement._id, testSetup.group._id, 2);

      // Check timeline events
      const timeline = await TimelineEvent.find({
        requirementId: requirement._id,
        eventType: 'state_changed'
      }).sort({ timestamp: 1 });

      expect(timeline.length).toBe(1);
      expect(timeline[0].eventData.from).toBe('New');
      expect(timeline[0].eventData.to).toBe('Being Drafted');
      expect(timeline[0].version).toBe(1);
    });

    test('should create timeline events when requirement version is created', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      // Update requirement to create new version
      const updateData = {
        title: 'Updated Timeline Test Requirement',
        description: 'Updated description for timeline testing',
        state: 'Being Drafted'
      };

      await request(app)
        .put(`/api/requirements/${requirement._id}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send(updateData);

      // Check timeline events
      const timeline = await TimelineEvent.find({
        requirementId: requirement._id
      }).sort({ timestamp: 1 });

      expect(timeline.length).toBeGreaterThan(2); // creation + version_created + changes

      const versionEvent = timeline.find(event => event.eventType === 'version_created');
      expect(versionEvent).toBeDefined();
      expect(versionEvent.version).toBe(2);
      expect(versionEvent.eventData.version).toBe(2);
    });

    test('should handle multiple event types in sequence', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      // Perform multiple operations
      await request(app)
        .post(`/api/requirements/${requirement._id}/transition-state`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({ newState: 'Being Drafted', version: 1 });

      await request(app)
        .post(`/api/requirements/${requirement._id}/transition-state`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({ newState: 'Requiring Approval', version: 1 });

      // Check timeline ordering
      const timeline = await TimelineEvent.find({
        requirementId: requirement._id
      }).sort({ timestamp: 1 });

      expect(timeline.length).toBeGreaterThanOrEqual(3); // created + 2 state changes

      // Verify chronological order
      for (let i = 1; i < timeline.length; i++) {
        expect(timeline[i].timestamp.getTime()).toBeGreaterThanOrEqual(
          timeline[i - 1].timestamp.getTime()
        );
      }
    });
  });

  describe('Timeline Service', () => {
    test('should get requirement timeline with proper filtering', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      // Wait for initial creation event
      await waitForTimelineEvents(requirement._id, testSetup.group._id, 1);

      // Add some state changes
      await request(app)
        .post(`/api/requirements/${requirement._id}/transition-state`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({ newState: 'Being Drafted', version: 1 });

      // Wait for state change event
      await waitForTimelineEvents(requirement._id, testSetup.group._id, 2);

      // Get timeline through service
      const timeline = await TimelineService.getRequirementTimeline(
        requirement._id,
        testSetup.group._id
      );

      expect(Array.isArray(timeline)).toBe(true);
      expect(timeline.length).toBeGreaterThan(0);

      // Check that events are properly populated
      const firstEvent = timeline[0];
      expect(firstEvent.user).toBeDefined();
      expect(firstEvent.user.username).toBeDefined();
    });

    test('should filter timeline by event type', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      // Wait for initial creation event
      await waitForTimelineEvents(requirement._id, testSetup.group._id, 1);

      // Add state change
      await request(app)
        .post(`/api/requirements/${requirement._id}/transition-state`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({ newState: 'Being Drafted', version: 1 });

      // Wait for state change event
      await waitForTimelineEvents(requirement._id, testSetup.group._id, 2);

      // Get only state change events
      const stateTimeline = await TimelineService.getRequirementTimeline(
        requirement._id,
        testSetup.group._id,
        { eventTypes: ['state_changed'] }
      );

      expect(stateTimeline.length).toBe(1);
      expect(stateTimeline[0].eventType).toBe('state_changed');
    });

    test('should filter timeline by version', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      // Create new version
      await request(app)
        .put(`/api/requirements/${requirement._id}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({
          title: 'Updated Title',
          description: 'Updated description'
        });

      // Get only version 1 events
      const version1Timeline = await TimelineService.getRequirementTimeline(
        requirement._id,
        testSetup.group._id,
        { version: 1 }
      );

      // Should include version-agnostic events and version 1 events
      expect(version1Timeline.length).toBeGreaterThan(0);
      
      const versionSpecificEvents = version1Timeline.filter(event => event.version !== null);
      versionSpecificEvents.forEach(event => {
        expect(event.version).toBeLessThanOrEqual(1);
      });
    });

    test('should get requirement with timeline combined', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      const result = await TimelineService.getRequirementWithTimeline(
        requirement._id,
        testSetup.group._id
      );

      expect(result.requirement).toBeDefined();
      expect(result.timeline).toBeDefined();
      expect(Array.isArray(result.timeline)).toBe(true);
      expect(result.requirement._id.toString()).toBe(requirement._id);
    });

    test('should get user activity timeline', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      // Add some activity
      await request(app)
        .post(`/api/requirements/${requirement._id}/transition-state`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({ newState: 'Being Drafted', version: 1 });

      const activity = await TimelineService.getUserActivity(
        testSetup.adminUser._id,
        testSetup.group._id
      );

      expect(Array.isArray(activity)).toBe(true);
      expect(activity.length).toBeGreaterThan(0);

      // All events should be from the specified user
      activity.forEach(event => {
        expect(event.user._id.toString()).toBe(testSetup.adminUser._id.toString());
      });
    });

    test('should get timeline statistics', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      // Add various events
      await request(app)
        .post(`/api/requirements/${requirement._id}/transition-state`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({ newState: 'Being Drafted', version: 1 });

      await request(app)
        .post(`/api/requirements/${requirement._id}/transition-state`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`)
        .send({ newState: 'Requiring Approval', version: 1 });

      const stats = await TimelineService.getTimelineStats(
        requirement._id,
        testSetup.group._id
      );

      expect(stats.totalEvents).toBeGreaterThan(0);
      expect(Array.isArray(stats.eventTypes)).toBe(true);
      expect(stats.eventTypes.length).toBeGreaterThan(0);
      
      // Check that stats include event type counts
      const stateChangeStats = stats.eventTypes.find(stat => stat._id === 'state_changed');
      expect(stateChangeStats).toBeDefined();
      expect(stateChangeStats.count).toBe(2);
    });
  });

  describe('Multi-Tenancy', () => {
    test('should isolate timeline events by group', async () => {
      // Create test data in default group
      const { requirement: req1 } = await createTestDataWithTimeline(testSetup);

      // Create second group and user
      const { createTestGroup, createTestUser, generateAuthToken } = require('../helpers/testHelpers');
      const group2 = await createTestGroup('timeline-test-2', 'Timeline Test Group 2', '1-3', testSetup.superUser);
      const user2 = await createTestUser({
        username: 'timeline-user-2',
        email: '<EMAIL>',
        firstName: 'Timeline',
        lastName: 'User2',
        groupStatus: 'active'
      }, group2);
      const token2 = generateAuthToken(user2);

      // Create requirement in second group
      const projectData2 = {
        name: 'Group 2 Project',
        description: 'Project for group 2'
      };

      const projectResponse2 = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${token2}`)
        .send(projectData2);

      const requirementData2 = {
        project: projectResponse2.body.elementId,
        title: 'Group 2 Requirement',
        description: 'Requirement for group 2',
        type: 'requirement'
      };

      const requirementResponse2 = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${token2}`)
        .send(requirementData2);

      const req2 = requirementResponse2.body;

      // Get timeline for group 1 requirement
      const timeline1 = await TimelineService.getRequirementTimeline(
        req1._id,
        testSetup.group._id
      );

      // Get timeline for group 2 requirement
      const timeline2 = await TimelineService.getRequirementTimeline(
        req2._id,
        group2._id
      );

      // Verify isolation
      expect(timeline1.length).toBeGreaterThan(0);
      expect(timeline2.length).toBeGreaterThan(0);

      // Verify group IDs
      timeline1.forEach(event => {
        expect(event.groupId.toString()).toBe(testSetup.group._id.toString());
      });

      timeline2.forEach(event => {
        expect(event.groupId.toString()).toBe(group2._id.toString());
      });

      // Try to access group 2 timeline with group 1 credentials (should be empty)
      const crossGroupTimeline = await TimelineService.getRequirementTimeline(
        req2._id,
        testSetup.group._id
      );

      expect(crossGroupTimeline.length).toBe(0);
    });
  });

  describe('Timeline API Routes', () => {
    test('should get timeline via API endpoint', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      const response = await request(app)
        .get(`/api/timeline/requirement/${requirement._id}`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });

    test('should get requirement with timeline via API', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      const response = await request(app)
        .get(`/api/timeline/requirement/${requirement._id}/with-requirement`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.requirement).toBeDefined();
      expect(response.body.timeline).toBeDefined();
      expect(Array.isArray(response.body.timeline)).toBe(true);
    });

    test('should get timeline stats via API', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      const response = await request(app)
        .get(`/api/timeline/requirement/${requirement._id}/stats`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.totalEvents).toBeGreaterThan(0);
      expect(Array.isArray(response.body.eventTypes)).toBe(true);
    });

    test('should get user activity via API', async () => {
      const { requirement } = await createTestDataWithTimeline(testSetup);

      const response = await request(app)
        .get(`/api/timeline/user/${testSetup.adminUser._id}/activity`)
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });

    test('should get valid event types via API', async () => {
      const response = await request(app)
        .get('/api/timeline/event-types')
        .set('Authorization', `Bearer ${testSetup.adminToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body.eventTypes)).toBe(true);
      expect(response.body.eventTypes).toContain('created');
      expect(response.body.eventTypes).toContain('state_changed');
      expect(response.body.eventTypes).toContain('version_created');
    });
  });
});

module.exports = { createTestDataWithTimeline };

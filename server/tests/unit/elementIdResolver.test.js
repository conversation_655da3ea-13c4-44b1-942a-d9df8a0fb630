const mongoose = require('mongoose');
const { validateElementId, parseElementId, generateProjectAcronym } = require('../../utils/elementIdGenerator');
const { createElementIdResolver } = require('../../middleware/elementIdResolver');

describe('ElementID Resolver Unit Tests', () => {
  
  describe('ElementID Validation', () => {
    test('should validate correct project ElementIDs', () => {
      const validProjectIds = [
        'PROJ-001',
        'TEST-123',
        'ABCD-999',
        'AB-001'
      ];

      validProjectIds.forEach(id => {
        expect(validateElementId(id)).toBe(true);
      });
    });

    test('should validate correct feature ElementIDs', () => {
      const validFeatureIds = [
        'PROJ-F-001',
        'TEST-F-123',
        'ABCD-F-999'
      ];

      validFeatureIds.forEach(id => {
        expect(validateElementId(id)).toBe(true);
      });
    });

    test('should validate correct requirement ElementIDs', () => {
      const validRequirementIds = [
        'PROJ-R-001',
        'TEST-R-123',
        'ABCD-R-999'
      ];

      validRequirementIds.forEach(id => {
        expect(validateElementId(id)).toBe(true);
      });
    });

    test('should reject invalid ElementIDs', () => {
      const invalidIds = [
        'invalid',
        '123-456',
        'TEST-X-001', // Invalid type
        'TEST-F-', // Missing sequence
        'TEST-F-ABC', // Non-numeric sequence
        'A-001', // Too short acronym
        'TOOLONG-F-001', // Too long acronym
        '', // Empty
        null, // Null
        undefined // Undefined
      ];

      invalidIds.forEach(id => {
        expect(validateElementId(id)).toBe(false);
      });
    });
  });

  describe('ElementID Parsing', () => {
    test('should parse project ElementIDs correctly', () => {
      const result = parseElementId('PROJ-001');
      expect(result).toEqual({
        acronym: 'PROJ',
        type: 'project',
        sequence: 1
      });
    });

    test('should parse feature ElementIDs correctly', () => {
      const result = parseElementId('TEST-F-123');
      expect(result).toEqual({
        acronym: 'TEST',
        type: 'feature',
        sequence: 123
      });
    });

    test('should parse requirement ElementIDs correctly', () => {
      const result = parseElementId('ABCD-R-999');
      expect(result).toEqual({
        acronym: 'ABCD',
        type: 'requirement',
        sequence: 999
      });
    });

    test('should throw error for invalid ElementIDs', () => {
      expect(() => parseElementId('invalid')).toThrow('Invalid ElementID format');
      expect(() => parseElementId('TEST-X-001')).toThrow();
    });
  });

  describe('Project Acronym Generation', () => {
    test('should generate acronyms from single words', () => {
      expect(generateProjectAcronym('Project')).toBe('PROJ');
      expect(generateProjectAcronym('Requirements')).toBe('REQU');
      expect(generateProjectAcronym('Test')).toBe('TEST');
    });

    test('should generate acronyms from multiple words', () => {
      expect(generateProjectAcronym('Project Management Tool')).toBe('PMT');
      expect(generateProjectAcronym('Requirements Management System')).toBe('RMS');
      expect(generateProjectAcronym('Test Case Manager')).toBe('TCM');
    });

    test('should handle special characters', () => {
      expect(generateProjectAcronym('Project-Management_Tool')).toBe('PMT');
      expect(generateProjectAcronym('Test & Quality Assurance')).toBe('TQA');
    });

    test('should limit to 4 characters for many words', () => {
      expect(generateProjectAcronym('Very Long Project Name With Many Words')).toBe('VLPN');
    });

    test('should handle edge cases', () => {
      expect(generateProjectAcronym('')).toBe('PROJ');
      expect(generateProjectAcronym('   ')).toBe('PROJ');
      expect(generateProjectAcronym('123')).toBe('123');
    });
  });

  describe('ObjectID vs ElementID Detection', () => {
    test('should detect valid ObjectIDs', () => {
      const validObjectId = new mongoose.Types.ObjectId().toString();
      expect(mongoose.Types.ObjectId.isValid(validObjectId)).toBe(true);
      expect(validObjectId.length).toBe(24);
    });

    test('should detect ElementIDs vs ObjectIDs', () => {
      const objectId = new mongoose.Types.ObjectId().toString();
      const elementId = 'PROJ-F-001';

      // ObjectID should be valid and 24 chars
      expect(mongoose.Types.ObjectId.isValid(objectId) && objectId.length === 24).toBe(true);
      
      // ElementID should not be valid ObjectID
      expect(mongoose.Types.ObjectId.isValid(elementId) && elementId.length === 24).toBe(false);
      
      // ElementID should be valid ElementID
      expect(validateElementId(elementId)).toBe(true);
    });
  });

  describe('URL Parameter Scenarios', () => {
    test('should handle common URL parameter patterns', () => {
      const scenarios = [
        { input: 'PROJ-001', expected: 'elementId', type: 'project' },
        { input: 'TEST-F-123', expected: 'elementId', type: 'feature' },
        { input: 'ABCD-R-999', expected: 'elementId', type: 'requirement' },
        { input: new mongoose.Types.ObjectId().toString(), expected: 'objectId', type: 'any' }
      ];

      scenarios.forEach(scenario => {
        if (scenario.expected === 'elementId') {
          expect(validateElementId(scenario.input)).toBe(true);
          expect(mongoose.Types.ObjectId.isValid(scenario.input) && scenario.input.length === 24).toBe(false);
        } else {
          expect(mongoose.Types.ObjectId.isValid(scenario.input) && scenario.input.length === 24).toBe(true);
          expect(validateElementId(scenario.input)).toBe(false);
        }
      });
    });
  });

  describe('Multi-tenancy Considerations', () => {
    test('should handle ElementID uniqueness within groups', () => {
      // ElementIDs should be unique within a group but can be duplicated across groups
      const elementId = 'PROJ-F-001';
      
      // Same ElementID can exist in different groups
      expect(validateElementId(elementId)).toBe(true);
      
      // The resolver should add group filtering
      // This would be tested in integration tests with actual database
    });
  });
});

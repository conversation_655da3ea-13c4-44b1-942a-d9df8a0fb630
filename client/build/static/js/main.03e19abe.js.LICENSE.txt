/*!
         * @overview es6-promise - a tiny implementation of Promises/A+.
         * @copyright Copyright (c) 2014 <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and contributors (Conversion to ES6 API by <PERSON>)
         * @license   Licensed under MIT license
         *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE
         * @version   v4.2.8+1e68dce6
         */

/*!
 * Quill Editor v1.3.7
 * https://quilljs.com/
 * Copyright (c) 2014, <PERSON>
 * Copyright (c) 2013, salesforce.com
 */

/*!
 * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
 * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>
 * Released under MIT License
 */

/*!
 * html2pdf.js v0.10.3
 * Copyright (c) 2025 <PERSON>
 * Released under the MIT License.
 */

/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.
    Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.
    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */

/*! ../internals/a-function */

/*! ../internals/a-possible-prototype */

/*! ../internals/add-to-unscopables */

/*! ../internals/an-object */

/*! ../internals/array-for-each */

/*! ../internals/array-includes */

/*! ../internals/array-iteration */

/*! ../internals/array-method-has-species-support */

/*! ../internals/array-method-is-strict */

/*! ../internals/array-species-constructor */

/*! ../internals/array-species-create */

/*! ../internals/classof */

/*! ../internals/classof-raw */

/*! ../internals/copy-constructor-properties */

/*! ../internals/correct-prototype-getter */

/*! ../internals/create-html */

/*! ../internals/create-iterator-constructor */

/*! ../internals/create-non-enumerable-property */

/*! ../internals/create-property */

/*! ../internals/create-property-descriptor */

/*! ../internals/define-iterator */

/*! ../internals/define-well-known-symbol */

/*! ../internals/descriptors */

/*! ../internals/document-create-element */

/*! ../internals/dom-iterables */

/*! ../internals/engine-user-agent */

/*! ../internals/engine-v8-version */

/*! ../internals/enum-bug-keys */

/*! ../internals/export */

/*! ../internals/fails */

/*! ../internals/function-bind-context */

/*! ../internals/get-built-in */

/*! ../internals/global */

/*! ../internals/has */

/*! ../internals/hidden-keys */

/*! ../internals/html */

/*! ../internals/ie8-dom-define */

/*! ../internals/indexed-object */

/*! ../internals/inherit-if-required */

/*! ../internals/inspect-source */

/*! ../internals/internal-state */

/*! ../internals/is-array */

/*! ../internals/is-forced */

/*! ../internals/is-object */

/*! ../internals/is-pure */

/*! ../internals/is-symbol */

/*! ../internals/iterators */

/*! ../internals/iterators-core */

/*! ../internals/native-symbol */

/*! ../internals/native-weak-map */

/*! ../internals/object-assign */

/*! ../internals/object-create */

/*! ../internals/object-define-properties */

/*! ../internals/object-define-property */

/*! ../internals/object-get-own-property-descriptor */

/*! ../internals/object-get-own-property-names */

/*! ../internals/object-get-own-property-names-external */

/*! ../internals/object-get-own-property-symbols */

/*! ../internals/object-get-prototype-of */

/*! ../internals/object-keys */

/*! ../internals/object-keys-internal */

/*! ../internals/object-property-is-enumerable */

/*! ../internals/object-set-prototype-of */

/*! ../internals/object-to-string */

/*! ../internals/ordinary-to-primitive */

/*! ../internals/own-keys */

/*! ../internals/path */

/*! ../internals/redefine */

/*! ../internals/regexp-flags */

/*! ../internals/require-object-coercible */

/*! ../internals/set-global */

/*! ../internals/set-to-string-tag */

/*! ../internals/shared */

/*! ../internals/shared-key */

/*! ../internals/shared-store */

/*! ../internals/string-html-forced */

/*! ../internals/string-multibyte */

/*! ../internals/string-trim */

/*! ../internals/to-absolute-index */

/*! ../internals/to-indexed-object */

/*! ../internals/to-integer */

/*! ../internals/to-length */

/*! ../internals/to-object */

/*! ../internals/to-primitive */

/*! ../internals/to-property-key */

/*! ../internals/to-string */

/*! ../internals/to-string-tag-support */

/*! ../internals/uid */

/*! ../internals/use-symbol-as-uid */

/*! ../internals/well-known-symbol */

/*! ../internals/well-known-symbol-wrapped */

/*! ../internals/whitespaces */

/*! ../modules/es.array.iterator */

/*! ../utils.js */

/*! ../worker.js */

/*! ./plugin/hyperlinks.js */

/*! ./plugin/jspdf-plugin.js */

/*! ./plugin/pagebreaks.js */

/*! ./utils.js */

/*! ./worker.js */

/*! core-js/modules/es.array.concat.js */

/*! core-js/modules/es.array.iterator.js */

/*! core-js/modules/es.array.join.js */

/*! core-js/modules/es.array.map.js */

/*! core-js/modules/es.array.slice.js */

/*! core-js/modules/es.function.name.js */

/*! core-js/modules/es.number.constructor.js */

/*! core-js/modules/es.object.assign.js */

/*! core-js/modules/es.object.keys.js */

/*! core-js/modules/es.object.to-string.js */

/*! core-js/modules/es.regexp.to-string.js */

/*! core-js/modules/es.string.iterator.js */

/*! core-js/modules/es.string.link.js */

/*! core-js/modules/es.symbol.description.js */

/*! core-js/modules/es.symbol.iterator.js */

/*! core-js/modules/es.symbol.js */

/*! core-js/modules/web.dom-collections.for-each.js */

/*! core-js/modules/web.dom-collections.iterator.js */

/*! es6-promise */

/*! html2canvas */

/*! jspdf */

/*!**********************!*\
        !*** ./src/index.js ***!
        \**********************/

/*!**********************!*\
        !*** ./src/utils.js ***!
        \**********************/

/*!***********************!*\
        !*** ./src/worker.js ***!
        \***********************/

/*!************************!*\
        !*** external "jspdf" ***!
        \************************/

/*!******************************!*\
        !*** external "html2canvas" ***!
        \******************************/

/*!**********************************!*\
        !*** ./src/plugin/hyperlinks.js ***!
        \**********************************/

/*!**********************************!*\
        !*** ./src/plugin/pagebreaks.js ***!
        \**********************************/

/*!************************************!*\
        !*** ./src/plugin/jspdf-plugin.js ***!
        \************************************/

/*!***********************************************!*\
        !*** ./node_modules/core-js/internals/has.js ***!
        \***********************************************/

/*!***********************************************!*\
        !*** ./node_modules/core-js/internals/uid.js ***!
        \***********************************************/

/*!************************************************!*\
        !*** ./node_modules/core-js/internals/html.js ***!
        \************************************************/

/*!************************************************!*\
        !*** ./node_modules/core-js/internals/path.js ***!
        \************************************************/

/*!*************************************************!*\
        !*** ./node_modules/core-js/internals/fails.js ***!
        \*************************************************/

/*!**************************************************!*\
        !*** ./node_modules/core-js/internals/export.js ***!
        \**************************************************/

/*!**************************************************!*\
        !*** ./node_modules/core-js/internals/global.js ***!
        \**************************************************/

/*!**************************************************!*\
        !*** ./node_modules/core-js/internals/shared.js ***!
        \**************************************************/

/*!***************************************************!*\
        !*** ./node_modules/core-js/internals/classof.js ***!
        \***************************************************/

/*!***************************************************!*\
        !*** ./node_modules/core-js/internals/is-pure.js ***!
        \***************************************************/

/*!***************************************************!*\
        !*** ./node_modules/core-js/modules/es.symbol.js ***!
        \***************************************************/

/*!****************************************************!*\
        !*** ./node_modules/core-js/internals/is-array.js ***!
        \****************************************************/

/*!****************************************************!*\
        !*** ./node_modules/core-js/internals/own-keys.js ***!
        \****************************************************/

/*!****************************************************!*\
        !*** ./node_modules/core-js/internals/redefine.js ***!
        \****************************************************/

/*!*****************************************************!*\
        !*** ./node_modules/core-js/internals/an-object.js ***!
        \*****************************************************/

/*!*****************************************************!*\
        !*** ./node_modules/core-js/internals/is-forced.js ***!
        \*****************************************************/

/*!*****************************************************!*\
        !*** ./node_modules/core-js/internals/is-object.js ***!
        \*****************************************************/

/*!*****************************************************!*\
        !*** ./node_modules/core-js/internals/is-symbol.js ***!
        \*****************************************************/

/*!*****************************************************!*\
        !*** ./node_modules/core-js/internals/iterators.js ***!
        \*****************************************************/

/*!*****************************************************!*\
        !*** ./node_modules/core-js/internals/to-length.js ***!
        \*****************************************************/

/*!*****************************************************!*\
        !*** ./node_modules/core-js/internals/to-object.js ***!
        \*****************************************************/

/*!*****************************************************!*\
        !*** ./node_modules/core-js/internals/to-string.js ***!
        \*****************************************************/

/*!******************************************************!*\
        !*** ./node_modules/core-js/internals/a-function.js ***!
        \******************************************************/

/*!******************************************************!*\
        !*** ./node_modules/core-js/internals/set-global.js ***!
        \******************************************************/

/*!******************************************************!*\
        !*** ./node_modules/core-js/internals/shared-key.js ***!
        \******************************************************/

/*!******************************************************!*\
        !*** ./node_modules/core-js/internals/to-integer.js ***!
        \******************************************************/

/*!******************************************************!*\
        !*** ./node_modules/core-js/modules/es.array.map.js ***!
        \******************************************************/

/*!******************************************************!*\
        !*** ./node_modules/es6-promise/dist/es6-promise.js ***!
        \******************************************************/

/*!*******************************************************!*\
        !*** ./node_modules/core-js/internals/classof-raw.js ***!
        \*******************************************************/

/*!*******************************************************!*\
        !*** ./node_modules/core-js/internals/create-html.js ***!
        \*******************************************************/

/*!*******************************************************!*\
        !*** ./node_modules/core-js/internals/descriptors.js ***!
        \*******************************************************/

/*!*******************************************************!*\
        !*** ./node_modules/core-js/internals/hidden-keys.js ***!
        \*******************************************************/

/*!*******************************************************!*\
        !*** ./node_modules/core-js/internals/object-keys.js ***!
        \*******************************************************/

/*!*******************************************************!*\
        !*** ./node_modules/core-js/internals/string-trim.js ***!
        \*******************************************************/

/*!*******************************************************!*\
        !*** ./node_modules/core-js/internals/whitespaces.js ***!
        \*******************************************************/

/*!*******************************************************!*\
        !*** ./node_modules/core-js/modules/es.array.join.js ***!
        \*******************************************************/

/*!********************************************************!*\
        !*** ./node_modules/core-js/internals/get-built-in.js ***!
        \********************************************************/

/*!********************************************************!*\
        !*** ./node_modules/core-js/internals/regexp-flags.js ***!
        \********************************************************/

/*!********************************************************!*\
        !*** ./node_modules/core-js/internals/shared-store.js ***!
        \********************************************************/

/*!********************************************************!*\
        !*** ./node_modules/core-js/internals/to-primitive.js ***!
        \********************************************************/

/*!********************************************************!*\
        !*** ./node_modules/core-js/modules/es.array.slice.js ***!
        \********************************************************/

/*!********************************************************!*\
        !*** ./node_modules/core-js/modules/es.object.keys.js ***!
        \********************************************************/

/*!********************************************************!*\
        !*** ./node_modules/core-js/modules/es.string.link.js ***!
        \********************************************************/

/*!*********************************************************!*\
        !*** ./node_modules/core-js/internals/dom-iterables.js ***!
        \*********************************************************/

/*!*********************************************************!*\
        !*** ./node_modules/core-js/internals/enum-bug-keys.js ***!
        \*********************************************************/

/*!*********************************************************!*\
        !*** ./node_modules/core-js/internals/native-symbol.js ***!
        \*********************************************************/

/*!*********************************************************!*\
        !*** ./node_modules/core-js/internals/object-assign.js ***!
        \*********************************************************/

/*!*********************************************************!*\
        !*** ./node_modules/core-js/internals/object-create.js ***!
        \*********************************************************/

/*!*********************************************************!*\
        !*** ./node_modules/core-js/modules/es.array.concat.js ***!
        \*********************************************************/

/*!**********************************************************!*\
        !*** ./node_modules/core-js/internals/array-for-each.js ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./node_modules/core-js/internals/array-includes.js ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./node_modules/core-js/internals/ie8-dom-define.js ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./node_modules/core-js/internals/indexed-object.js ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./node_modules/core-js/internals/inspect-source.js ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./node_modules/core-js/internals/internal-state.js ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./node_modules/core-js/internals/iterators-core.js ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./node_modules/core-js/modules/es.function.name.js ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./node_modules/core-js/modules/es.object.assign.js ***!
        \**********************************************************/

/*!***********************************************************!*\
        !*** ./node_modules/core-js/internals/array-iteration.js ***!
        \***********************************************************/

/*!***********************************************************!*\
        !*** ./node_modules/core-js/internals/create-property.js ***!
        \***********************************************************/

/*!***********************************************************!*\
        !*** ./node_modules/core-js/internals/define-iterator.js ***!
        \***********************************************************/

/*!***********************************************************!*\
        !*** ./node_modules/core-js/internals/native-weak-map.js ***!
        \***********************************************************/

/*!***********************************************************!*\
        !*** ./node_modules/core-js/internals/to-property-key.js ***!
        \***********************************************************/

/*!***********************************************************!*\
        !*** ./node_modules/core-js/modules/es.array.iterator.js ***!
        \***********************************************************/

/*!************************************************************!*\
        !*** ./node_modules/core-js/internals/object-to-string.js ***!
        \************************************************************/

/*!************************************************************!*\
        !*** ./node_modules/core-js/internals/string-multibyte.js ***!
        \************************************************************/

/*!************************************************************!*\
        !*** ./node_modules/core-js/modules/es.string.iterator.js ***!
        \************************************************************/

/*!************************************************************!*\
        !*** ./node_modules/core-js/modules/es.symbol.iterator.js ***!
        \************************************************************/

/*!*************************************************************!*\
        !*** ./node_modules/core-js/internals/engine-user-agent.js ***!
        \*************************************************************/

/*!*************************************************************!*\
        !*** ./node_modules/core-js/internals/engine-v8-version.js ***!
        \*************************************************************/

/*!*************************************************************!*\
        !*** ./node_modules/core-js/internals/set-to-string-tag.js ***!
        \*************************************************************/

/*!*************************************************************!*\
        !*** ./node_modules/core-js/internals/to-absolute-index.js ***!
        \*************************************************************/

/*!*************************************************************!*\
        !*** ./node_modules/core-js/internals/to-indexed-object.js ***!
        \*************************************************************/

/*!*************************************************************!*\
        !*** ./node_modules/core-js/internals/use-symbol-as-uid.js ***!
        \*************************************************************/

/*!*************************************************************!*\
        !*** ./node_modules/core-js/internals/well-known-symbol.js ***!
        \*************************************************************/

/*!*************************************************************!*\
        !*** ./node_modules/core-js/modules/es.object.to-string.js ***!
        \*************************************************************/

/*!*************************************************************!*\
        !*** ./node_modules/core-js/modules/es.regexp.to-string.js ***!
        \*************************************************************/

/*!**************************************************************!*\
        !*** ./node_modules/core-js/internals/add-to-unscopables.js ***!
        \**************************************************************/

/*!**************************************************************!*\
        !*** ./node_modules/core-js/internals/string-html-forced.js ***!
        \**************************************************************/

/*!***************************************************************!*\
        !*** ./node_modules/core-js/internals/inherit-if-required.js ***!
        \***************************************************************/

/*!***************************************************************!*\
        !*** ./node_modules/core-js/modules/es.number.constructor.js ***!
        \***************************************************************/

/*!***************************************************************!*\
        !*** ./node_modules/core-js/modules/es.symbol.description.js ***!
        \***************************************************************/

/*!****************************************************************!*\
        !*** ./node_modules/core-js/internals/a-possible-prototype.js ***!
        \****************************************************************/

/*!****************************************************************!*\
        !*** ./node_modules/core-js/internals/array-species-create.js ***!
        \****************************************************************/

/*!****************************************************************!*\
        !*** ./node_modules/core-js/internals/object-keys-internal.js ***!
        \****************************************************************/

/*!*****************************************************************!*\
        !*** ./node_modules/core-js/internals/function-bind-context.js ***!
        \*****************************************************************/

/*!*****************************************************************!*\
        !*** ./node_modules/core-js/internals/ordinary-to-primitive.js ***!
        \*****************************************************************/

/*!*****************************************************************!*\
        !*** ./node_modules/core-js/internals/to-string-tag-support.js ***!
        \*****************************************************************/

/*!******************************************************************!*\
        !*** ./node_modules/core-js/internals/array-method-is-strict.js ***!
        \******************************************************************/

/*!******************************************************************!*\
        !*** ./node_modules/core-js/internals/object-define-property.js ***!
        \******************************************************************/

/*!*******************************************************************!*\
        !*** ./node_modules/core-js/internals/document-create-element.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./node_modules/core-js/internals/object-get-prototype-of.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./node_modules/core-js/internals/object-set-prototype-of.js ***!
        \*******************************************************************/

/*!********************************************************************!*\
        !*** ./node_modules/core-js/internals/correct-prototype-getter.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./node_modules/core-js/internals/define-well-known-symbol.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./node_modules/core-js/internals/object-define-properties.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./node_modules/core-js/internals/require-object-coercible.js ***!
        \********************************************************************/

/*!*********************************************************************!*\
        !*** ./node_modules/core-js/internals/array-species-constructor.js ***!
        \*********************************************************************/

/*!*********************************************************************!*\
        !*** ./node_modules/core-js/internals/well-known-symbol-wrapped.js ***!
        \*********************************************************************/

/*!**********************************************************************!*\
        !*** ./node_modules/core-js/internals/create-property-descriptor.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./node_modules/core-js/modules/web.dom-collections.for-each.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./node_modules/core-js/modules/web.dom-collections.iterator.js ***!
        \**********************************************************************/

/*!***********************************************************************!*\
        !*** ./node_modules/core-js/internals/copy-constructor-properties.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./node_modules/core-js/internals/create-iterator-constructor.js ***!
        \***********************************************************************/

/*!*************************************************************************!*\
        !*** ./node_modules/core-js/internals/object-get-own-property-names.js ***!
        \*************************************************************************/

/*!*************************************************************************!*\
        !*** ./node_modules/core-js/internals/object-property-is-enumerable.js ***!
        \*************************************************************************/

/*!**************************************************************************!*\
        !*** ./node_modules/core-js/internals/create-non-enumerable-property.js ***!
        \**************************************************************************/

/*!***************************************************************************!*\
        !*** ./node_modules/core-js/internals/object-get-own-property-symbols.js ***!
        \***************************************************************************/

/*!****************************************************************************!*\
        !*** ./node_modules/core-js/internals/array-method-has-species-support.js ***!
        \****************************************************************************/

/*!******************************************************************************!*\
        !*** ./node_modules/core-js/internals/object-get-own-property-descriptor.js ***!
        \******************************************************************************/

/*!**********************************************************************************!*\
        !*** ./node_modules/core-js/internals/object-get-own-property-names-external.js ***!
        \**********************************************************************************/

/**
 * @license
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

/**
 * @license
 *
 * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 */

/**
 * @license
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * (c) Dean McNamee <<EMAIL>>, 2013.
 *
 * https://github.com/deanm/omggif
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * omggif is a JavaScript implementation of a GIF 89a encoder and decoder,
 * including animation and compression.  It does not rely on any specific
 * underlying system, so should run in the browser, Node, or Plask.
 */

/**
 * @license
 * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * Copyright (c) 2018 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * Copyright (c) 2019 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * FPDF is released under a permissive license: there is no usage restriction.
 * You may embed it freely in your application (commercial or not), with or
 * without modifications.
 *
 * Reference: http://www.fpdf.org/en/script/script37.php
 */

/**
 * @license
 * Joseph Myers does not specify a particular license for his work.
 *
 * Author: Joseph Myers
 * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
 *
 * Modified by: Owen Leong
 */

/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 * Author: Owen Leong (@owenl131)
 * Date: 15 Oct 2020
 * References:
 * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
 * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
 * http://www.fpdf.org/en/script/script37.php
 */

/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * Unicode Bidi Engine based on the work of Alex Shensis (@asthensis)
 * MIT License
 */

/**
 * @license
 * jsPDF fileloading PlugIn
 * Copyright (c) 2018 Aras Abbasi (<EMAIL>)
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * jsPDF filters PlugIn
 * Copyright (c) 2014 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * jsPDF virtual FileSystem functionality
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */

/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
 * @license Use it if you like it
 */

/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */

/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */

/** ====================================================================
 * @license
 * jsPDF XMP metadata plugin
 * Copyright (c) 2016 Jussi Utunen, <EMAIL>
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 */

/** @license
         * Copyright (c) 2017 Dominik Homberger
        Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
        The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
        https://webpjs.appspot.com
        WebPRiffParser <EMAIL>
        */

/** @license
 *
 * jsPDF - PDF Document creation from JavaScript
 * Version 3.0.1 Built on 2025-03-17T14:19:36.873Z
 *                      CommitID 00000000
 *
 * Copyright (c) 2010-2021 James Hall <<EMAIL>>, https://github.com/MrRio/jsPDF
 *               2015-2021 yWorks GmbH, http://www.yworks.com
 *               2015-2021 Lukas Holländer <<EMAIL>>, https://github.com/HackbrettXXX
 *               2016-2018 Aras Abbasi <<EMAIL>>
 *               2010 Aaron Spike, https://github.com/acspike
 *               2012 Willow Systems Corporation, https://github.com/willowsystems
 *               2012 Pablo Hess, https://github.com/pablohess
 *               2012 Florian Jenett, https://github.com/fjenett
 *               2013 Warren Weckesser, https://github.com/warrenweckesser
 *               2013 Youssef Beddad, https://github.com/lifof
 *               2013 Lee Driscoll, https://github.com/lsdriscoll
 *               2013 Stefan Slonevskiy, https://github.com/stefslon
 *               2013 Jeremy Morel, https://github.com/jmorel
 *               2013 Christoph Hartmann, https://github.com/chris-rock
 *               2014 Juan Pablo Gaviria, https://github.com/juanpgaviria
 *               2014 James Makes, https://github.com/dollaruw
 *               2014 Diego Casorran, https://github.com/diegocr
 *               2014 Steven Spungin, https://github.com/Flamenco
 *               2014 Kenneth Glassey, https://github.com/Gavvers
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 * Contributor(s):
 *    siefkenj, ahwolf, rickygu, Midnith, saintclair, eaparango,
 *    kim3er, mfo, alnorth, Flamenco
 */

/** @license
 * Copyright (c) 2012 Willow Systems Corporation, https://github.com/willowsystems
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 */

/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

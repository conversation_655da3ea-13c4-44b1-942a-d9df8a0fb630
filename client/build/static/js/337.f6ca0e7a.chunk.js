(self.webpackChunkrequirements_tool_client=self.webpackChunkrequirements_tool_client||[]).push([[337],{1700:(t,e,r)=>{for(var i=r(4721),n="undefined"===typeof window?r.g:window,s=["moz","webkit"],a="AnimationFrame",o=n["request"+a],h=n["cancel"+a]||n["cancelRequest"+a],l=0;!o&&l<s.length;l++)o=n[s[l]+"Request"+a],h=n[s[l]+"Cancel"+a]||n[s[l]+"CancelRequest"+a];if(!o||!h){var u=0,c=0,g=[],d=1e3/60;o=function(t){if(0===g.length){var e=i(),r=Math.max(0,d-(e-u));u=r+e,setTimeout((function(){var t=g.slice(0);g.length=0;for(var e=0;e<t.length;e++)if(!t[e].cancelled)try{t[e].callback(u)}catch(r){setTimeout((function(){throw r}),0)}}),Math.round(r))}return g.push({handle:++c,callback:t,cancelled:!1}),c},h=function(t){for(var e=0;e<g.length;e++)g[e].handle===t&&(g[e].cancelled=!0)}}t.exports=function(t){return o.call(n,t)},t.exports.cancel=function(){h.apply(n,arguments)},t.exports.polyfill=function(t){t||(t=n),t.requestAnimationFrame=o,t.cancelAnimationFrame=h}},3113:t=>{t.exports=function(t){this.ok=!1,this.alpha=1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=(t=t.replace(/ /g,"")).toLowerCase();var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};t=e[t]||t;for(var r=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3]),parseFloat(t[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],i=0;i<r.length;i++){var n=r[i].re,s=r[i].process,a=n.exec(t);if(a){var o=s(a);this.r=o[0],this.g=o[1],this.b=o[2],o.length>3&&(this.alpha=o[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r},this.getHelpXML=function(){for(var t=new Array,i=0;i<r.length;i++)for(var n=r[i].example,s=0;s<n.length;s++)t[t.length]=n[s];for(var a in e)t[t.length]=a;var o=document.createElement("ul");o.setAttribute("id","rgbcolor-examples");for(i=0;i<t.length;i++)try{var h=document.createElement("li"),l=new RGBColor(t[i]),u=document.createElement("div");u.style.cssText="margin: 3px; border: 1px solid black; background:"+l.toHex()+"; color:"+l.toHex(),u.appendChild(document.createTextNode("test"));var c=document.createTextNode(" "+t[i]+" -> "+l.toRGB()+" -> "+l.toHex());h.appendChild(u),h.appendChild(c),o.appendChild(h)}catch(g){}return o}}},4721:function(t){(function(){var e,r,i,n,s,a;"undefined"!==typeof performance&&null!==performance&&performance.now?t.exports=function(){return performance.now()}:"undefined"!==typeof process&&null!==process&&process.hrtime?(t.exports=function(){return(e()-s)/1e6},r=process.hrtime,n=(e=function(){var t;return 1e9*(t=r())[0]+t[1]})(),a=1e9*process.uptime(),s=n-a):Date.now?(t.exports=function(){return Date.now()-i},i=Date.now()):(t.exports=function(){return(new Date).getTime()-i},i=(new Date).getTime())}).call(this)},5337:(t,e,r)=>{"use strict";function i(t,e,r,i,n,s,a){try{var o=t[s](a),h=o.value}catch(t){return void r(t)}o.done?e(h):Promise.resolve(h).then(i,n)}function n(t){return function(){var e=this,r=arguments;return new Promise((function(n,s){var a=t.apply(e,r);function o(t){i(a,n,s,o,h,"next",t)}function h(t){i(a,n,s,o,h,"throw",t)}o(void 0)}))}}r.r(e),r.d(e,{AElement:()=>ue,AnimateColorElement:()=>ne,AnimateElement:()=>ie,AnimateTransformElement:()=>se,BoundingBox:()=>Rt,CB1:()=>it,CB2:()=>nt,CB3:()=>st,CB4:()=>at,Canvg:()=>Fe,CircleElement:()=>Yt,ClipPathElement:()=>Oe,DefsElement:()=>Zt,DescElement:()=>Re,Document:()=>Be,Element:()=>Et,EllipseElement:()=>jt,FeColorMatrixElement:()=>Ae,FeCompositeElement:()=>Ve,FeDropShadowElement:()=>Me,FeGaussianBlurElement:()=>_e,FeMorphologyElement:()=>Ne,FilterElement:()=>Ee,Font:()=>kt,FontElement:()=>ae,FontFaceElement:()=>oe,GElement:()=>Kt,GlyphElement:()=>Bt,GradientElement:()=>Jt,ImageElement:()=>fe,LineElement:()=>qt,LinearGradientElement:()=>te,MarkerElement:()=>$t,MaskElement:()=>Ce,Matrix:()=>Tt,MissingGlyphElement:()=>he,Mouse:()=>dt,PSEUDO_ZERO:()=>J,Parser:()=>xt,PathElement:()=>Dt,PathParser:()=>It,PatternElement:()=>Gt,Point:()=>gt,PolygonElement:()=>Wt,PolylineElement:()=>Qt,Property:()=>ut,QB1:()=>ot,QB2:()=>ht,QB3:()=>lt,RadialGradientElement:()=>ee,RectElement:()=>Xt,RenderedElement:()=>Lt,Rotate:()=>St,SVGElement:()=>Ut,SVGFontLoader:()=>me,Scale:()=>wt,Screen:()=>yt,Skew:()=>At,SkewX:()=>Ct,SkewY:()=>Pt,StopElement:()=>re,StyleElement:()=>ve,SymbolElement:()=>ye,TRefElement:()=>le,TSpanElement:()=>Ht,TextElement:()=>zt,TextPathElement:()=>de,TitleElement:()=>ke,Transform:()=>Ot,Translate:()=>bt,UnknownElement:()=>Mt,UseElement:()=>xe,ViewPort:()=>ct,compressSpaces:()=>L,default:()=>Fe,getSelectorSpecificity:()=>K,normalizeAttributeName:()=>F,normalizeColor:()=>X,parseExternalUrl:()=>U,presets:()=>I,toNumbers:()=>z,trimLeft:()=>D,trimRight:()=>B,vectorMagnitude:()=>tt,vectorsAngle:()=>rt,vectorsRatio:()=>et});var s=r(816);function a(t,e,r){return(e=(0,s.A)(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var o=r(1700),h=r(3113),l=function(t,e){return(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function u(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function c(t,e){var r=t[0],i=t[1];return[r*Math.cos(e)-i*Math.sin(e),r*Math.sin(e)+i*Math.cos(e)]}function g(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0;r<t.length;r++)if("number"!=typeof t[r])throw new Error("assertNumbers arguments["+r+"] is not a number. "+typeof t[r]+" == typeof "+t[r]);return!0}var d=Math.PI;function p(t,e,r){t.lArcFlag=0===t.lArcFlag?0:1,t.sweepFlag=0===t.sweepFlag?0:1;var i=t.rX,n=t.rY,s=t.x,a=t.y;i=Math.abs(t.rX),n=Math.abs(t.rY);var o=c([(e-s)/2,(r-a)/2],-t.xRot/180*d),h=o[0],l=o[1],u=Math.pow(h,2)/Math.pow(i,2)+Math.pow(l,2)/Math.pow(n,2);1<u&&(i*=Math.sqrt(u),n*=Math.sqrt(u)),t.rX=i,t.rY=n;var g=Math.pow(i,2)*Math.pow(l,2)+Math.pow(n,2)*Math.pow(h,2),p=(t.lArcFlag!==t.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(i,2)*Math.pow(n,2)-g)/g)),f=i*l/n*p,y=-n*h/i*p,m=c([f,y],t.xRot/180*d);t.cX=m[0]+(e+s)/2,t.cY=m[1]+(r+a)/2,t.phi1=Math.atan2((l-y)/n,(h-f)/i),t.phi2=Math.atan2((-l-y)/n,(-h-f)/i),0===t.sweepFlag&&t.phi2>t.phi1&&(t.phi2-=2*d),1===t.sweepFlag&&t.phi2<t.phi1&&(t.phi2+=2*d),t.phi1*=180/d,t.phi2*=180/d}function f(t,e,r){g(t,e,r);var i=t*t+e*e-r*r;if(0>i)return[];if(0===i)return[[t*r/(t*t+e*e),e*r/(t*t+e*e)]];var n=Math.sqrt(i);return[[(t*r+e*n)/(t*t+e*e),(e*r-t*n)/(t*t+e*e)],[(t*r-e*n)/(t*t+e*e),(e*r+t*n)/(t*t+e*e)]]}var y,m=Math.PI/180;function v(t,e,r){return(1-r)*t+r*e}function x(t,e,r,i){return t+Math.cos(i/180*d)*e+Math.sin(i/180*d)*r}function b(t,e,r,i){var n=1e-6,s=e-t,a=r-e,o=3*s+3*(i-r)-6*a,h=6*(a-s),l=3*s;return Math.abs(o)<n?[-l/h]:function(t,e,r){void 0===r&&(r=1e-6);var i=t*t/4-e;if(i<-r)return[];if(i<=r)return[-t/2];var n=Math.sqrt(i);return[-t/2-n,-t/2+n]}(h/o,l/o,n)}function S(t,e,r,i,n){var s=1-n;return t*(s*s*s)+e*(3*s*s*n)+r*(3*s*n*n)+i*(n*n*n)}!function(t){function e(){return n((function(t,e,r){return t.relative&&(void 0!==t.x1&&(t.x1+=e),void 0!==t.y1&&(t.y1+=r),void 0!==t.x2&&(t.x2+=e),void 0!==t.y2&&(t.y2+=r),void 0!==t.x&&(t.x+=e),void 0!==t.y&&(t.y+=r),t.relative=!1),t}))}function r(){var t=NaN,e=NaN,r=NaN,i=NaN;return n((function(n,s,a){return n.type&O.SMOOTH_CURVE_TO&&(n.type=O.CURVE_TO,t=isNaN(t)?s:t,e=isNaN(e)?a:e,n.x1=n.relative?s-t:2*s-t,n.y1=n.relative?a-e:2*a-e),n.type&O.CURVE_TO?(t=n.relative?s+n.x2:n.x2,e=n.relative?a+n.y2:n.y2):(t=NaN,e=NaN),n.type&O.SMOOTH_QUAD_TO&&(n.type=O.QUAD_TO,r=isNaN(r)?s:r,i=isNaN(i)?a:i,n.x1=n.relative?s-r:2*s-r,n.y1=n.relative?a-i:2*a-i),n.type&O.QUAD_TO?(r=n.relative?s+n.x1:n.x1,i=n.relative?a+n.y1:n.y1):(r=NaN,i=NaN),n}))}function i(){var t=NaN,e=NaN;return n((function(r,i,n){if(r.type&O.SMOOTH_QUAD_TO&&(r.type=O.QUAD_TO,t=isNaN(t)?i:t,e=isNaN(e)?n:e,r.x1=r.relative?i-t:2*i-t,r.y1=r.relative?n-e:2*n-e),r.type&O.QUAD_TO){t=r.relative?i+r.x1:r.x1,e=r.relative?n+r.y1:r.y1;var s=r.x1,a=r.y1;r.type=O.CURVE_TO,r.x1=((r.relative?0:i)+2*s)/3,r.y1=((r.relative?0:n)+2*a)/3,r.x2=(r.x+2*s)/3,r.y2=(r.y+2*a)/3}else t=NaN,e=NaN;return r}))}function n(t){var e=0,r=0,i=NaN,n=NaN;return function(s){if(isNaN(i)&&!(s.type&O.MOVE_TO))throw new Error("path must start with moveto");var a=t(s,e,r,i,n);return s.type&O.CLOSE_PATH&&(e=i,r=n),void 0!==s.x&&(e=s.relative?e+s.x:s.x),void 0!==s.y&&(r=s.relative?r+s.y:s.y),s.type&O.MOVE_TO&&(i=e,n=r),a}}function s(t,e,r,i,s,a){return g(t,e,r,i,s,a),n((function(n,o,h,l){var u=n.x1,c=n.x2,g=n.relative&&!isNaN(l),d=void 0!==n.x?n.x:g?0:o,p=void 0!==n.y?n.y:g?0:h;function f(t){return t*t}n.type&O.HORIZ_LINE_TO&&0!==e&&(n.type=O.LINE_TO,n.y=n.relative?0:h),n.type&O.VERT_LINE_TO&&0!==r&&(n.type=O.LINE_TO,n.x=n.relative?0:o),void 0!==n.x&&(n.x=n.x*t+p*r+(g?0:s)),void 0!==n.y&&(n.y=d*e+n.y*i+(g?0:a)),void 0!==n.x1&&(n.x1=n.x1*t+n.y1*r+(g?0:s)),void 0!==n.y1&&(n.y1=u*e+n.y1*i+(g?0:a)),void 0!==n.x2&&(n.x2=n.x2*t+n.y2*r+(g?0:s)),void 0!==n.y2&&(n.y2=c*e+n.y2*i+(g?0:a));var y=t*i-e*r;if(void 0!==n.xRot&&(1!==t||0!==e||0!==r||1!==i))if(0===y)delete n.rX,delete n.rY,delete n.xRot,delete n.lArcFlag,delete n.sweepFlag,n.type=O.LINE_TO;else{var m=n.xRot*Math.PI/180,v=Math.sin(m),x=Math.cos(m),b=1/f(n.rX),S=1/f(n.rY),w=f(x)*b+f(v)*S,T=2*v*x*(b-S),A=f(v)*b+f(x)*S,C=w*i*i-T*e*i+A*e*e,P=T*(t*i+e*r)-2*(w*r*i+A*t*e),E=w*r*r-T*t*r+A*t*t,M=(Math.atan2(P,C-E)+Math.PI)%Math.PI/2,N=Math.sin(M),V=Math.cos(M);n.rX=Math.abs(y)/Math.sqrt(C*f(V)+P*N*V+E*f(N)),n.rY=Math.abs(y)/Math.sqrt(C*f(N)-P*N*V+E*f(V)),n.xRot=180*M/Math.PI}return void 0!==n.sweepFlag&&0>y&&(n.sweepFlag=+!n.sweepFlag),n}))}t.ROUND=function(t){function e(e){return Math.round(e*t)/t}return void 0===t&&(t=1e13),g(t),function(t){return void 0!==t.x1&&(t.x1=e(t.x1)),void 0!==t.y1&&(t.y1=e(t.y1)),void 0!==t.x2&&(t.x2=e(t.x2)),void 0!==t.y2&&(t.y2=e(t.y2)),void 0!==t.x&&(t.x=e(t.x)),void 0!==t.y&&(t.y=e(t.y)),void 0!==t.rX&&(t.rX=e(t.rX)),void 0!==t.rY&&(t.rY=e(t.rY)),t}},t.TO_ABS=e,t.TO_REL=function(){return n((function(t,e,r){return t.relative||(void 0!==t.x1&&(t.x1-=e),void 0!==t.y1&&(t.y1-=r),void 0!==t.x2&&(t.x2-=e),void 0!==t.y2&&(t.y2-=r),void 0!==t.x&&(t.x-=e),void 0!==t.y&&(t.y-=r),t.relative=!0),t}))},t.NORMALIZE_HVZ=function(t,e,r){return void 0===t&&(t=!0),void 0===e&&(e=!0),void 0===r&&(r=!0),n((function(i,n,s,a,o){if(isNaN(a)&&!(i.type&O.MOVE_TO))throw new Error("path must start with moveto");return e&&i.type&O.HORIZ_LINE_TO&&(i.type=O.LINE_TO,i.y=i.relative?0:s),r&&i.type&O.VERT_LINE_TO&&(i.type=O.LINE_TO,i.x=i.relative?0:n),t&&i.type&O.CLOSE_PATH&&(i.type=O.LINE_TO,i.x=i.relative?a-n:a,i.y=i.relative?o-s:o),i.type&O.ARC&&(0===i.rX||0===i.rY)&&(i.type=O.LINE_TO,delete i.rX,delete i.rY,delete i.xRot,delete i.lArcFlag,delete i.sweepFlag),i}))},t.NORMALIZE_ST=r,t.QT_TO_C=i,t.INFO=n,t.SANITIZE=function(t){void 0===t&&(t=0),g(t);var e=NaN,r=NaN,i=NaN,s=NaN;return n((function(n,a,o,h,l){var u=Math.abs,c=!1,g=0,d=0;if(n.type&O.SMOOTH_CURVE_TO&&(g=isNaN(e)?0:a-e,d=isNaN(r)?0:o-r),n.type&(O.CURVE_TO|O.SMOOTH_CURVE_TO)?(e=n.relative?a+n.x2:n.x2,r=n.relative?o+n.y2:n.y2):(e=NaN,r=NaN),n.type&O.SMOOTH_QUAD_TO?(i=isNaN(i)?a:2*a-i,s=isNaN(s)?o:2*o-s):n.type&O.QUAD_TO?(i=n.relative?a+n.x1:n.x1,s=n.relative?o+n.y1:n.y2):(i=NaN,s=NaN),n.type&O.LINE_COMMANDS||n.type&O.ARC&&(0===n.rX||0===n.rY||!n.lArcFlag)||n.type&O.CURVE_TO||n.type&O.SMOOTH_CURVE_TO||n.type&O.QUAD_TO||n.type&O.SMOOTH_QUAD_TO){var p=void 0===n.x?0:n.relative?n.x:n.x-a,f=void 0===n.y?0:n.relative?n.y:n.y-o;g=isNaN(i)?void 0===n.x1?g:n.relative?n.x:n.x1-a:i-a,d=isNaN(s)?void 0===n.y1?d:n.relative?n.y:n.y1-o:s-o;var y=void 0===n.x2?0:n.relative?n.x:n.x2-a,m=void 0===n.y2?0:n.relative?n.y:n.y2-o;u(p)<=t&&u(f)<=t&&u(g)<=t&&u(d)<=t&&u(y)<=t&&u(m)<=t&&(c=!0)}return n.type&O.CLOSE_PATH&&u(a-h)<=t&&u(o-l)<=t&&(c=!0),c?[]:n}))},t.MATRIX=s,t.ROTATE=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0),g(t,e,r);var i=Math.sin(t),n=Math.cos(t);return s(n,i,-i,n,e-e*n+r*i,r-e*i-r*n)},t.TRANSLATE=function(t,e){return void 0===e&&(e=0),g(t,e),s(1,0,0,1,t,e)},t.SCALE=function(t,e){return void 0===e&&(e=t),g(t,e),s(t,0,0,e,0,0)},t.SKEW_X=function(t){return g(t),s(1,0,Math.atan(t),1,0,0)},t.SKEW_Y=function(t){return g(t),s(1,Math.atan(t),0,1,0,0)},t.X_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),g(t),s(-1,0,0,1,t,0)},t.Y_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),g(t),s(1,0,0,-1,0,t)},t.A_TO_C=function(){return n((function(t,e,r){return O.ARC===t.type?function(t,e,r){var i,n,s,a;t.cX||p(t,e,r);for(var o=Math.min(t.phi1,t.phi2),h=Math.max(t.phi1,t.phi2)-o,l=Math.ceil(h/90),u=new Array(l),g=e,d=r,f=0;f<l;f++){var y=v(t.phi1,t.phi2,f/l),x=v(t.phi1,t.phi2,(f+1)/l),b=x-y,S=4/3*Math.tan(b*m/4),w=[Math.cos(y*m)-S*Math.sin(y*m),Math.sin(y*m)+S*Math.cos(y*m)],T=w[0],A=w[1],C=[Math.cos(x*m),Math.sin(x*m)],P=C[0],E=C[1],M=[P+S*Math.sin(x*m),E-S*Math.cos(x*m)],N=M[0],V=M[1];u[f]={relative:t.relative,type:O.CURVE_TO};var _=function(e,r){var i=c([e*t.rX,r*t.rY],t.xRot),n=i[0],s=i[1];return[t.cX+n,t.cY+s]};i=_(T,A),u[f].x1=i[0],u[f].y1=i[1],n=_(N,V),u[f].x2=n[0],u[f].y2=n[1],s=_(P,E),u[f].x=s[0],u[f].y=s[1],t.relative&&(u[f].x1-=g,u[f].y1-=d,u[f].x2-=g,u[f].y2-=d,u[f].x-=g,u[f].y-=d),g=(a=[u[f].x,u[f].y])[0],d=a[1]}return u}(t,t.relative?0:e,t.relative?0:r):t}))},t.ANNOTATE_ARCS=function(){return n((function(t,e,r){return t.relative&&(e=0,r=0),O.ARC===t.type&&p(t,e,r),t}))},t.CLONE=function(){return function(t){var e={};for(var r in t)e[r]=t[r];return e}},t.CALCULATE_BOUNDS=function(){var t=e(),s=i(),a=r(),o=n((function(e,r,i){var n=a(s(t(function(t){var e={};for(var r in t)e[r]=t[r];return e}(e))));function h(t){t>o.maxX&&(o.maxX=t),t<o.minX&&(o.minX=t)}function l(t){t>o.maxY&&(o.maxY=t),t<o.minY&&(o.minY=t)}if(n.type&O.DRAWING_COMMANDS&&(h(r),l(i)),n.type&O.HORIZ_LINE_TO&&h(n.x),n.type&O.VERT_LINE_TO&&l(n.y),n.type&O.LINE_TO&&(h(n.x),l(n.y)),n.type&O.CURVE_TO){h(n.x),l(n.y);for(var u=0,c=b(r,n.x1,n.x2,n.x);u<c.length;u++)0<(k=c[u])&&1>k&&h(S(r,n.x1,n.x2,n.x,k));for(var g=0,d=b(i,n.y1,n.y2,n.y);g<d.length;g++)0<(k=d[g])&&1>k&&l(S(i,n.y1,n.y2,n.y,k))}if(n.type&O.ARC){h(n.x),l(n.y),p(n,r,i);for(var y=n.xRot/180*Math.PI,m=Math.cos(y)*n.rX,v=Math.sin(y)*n.rX,w=-Math.sin(y)*n.rY,T=Math.cos(y)*n.rY,A=n.phi1<n.phi2?[n.phi1,n.phi2]:-180>n.phi2?[n.phi2+360,n.phi1+360]:[n.phi2,n.phi1],C=A[0],P=A[1],E=function(t){var e=t[0],r=t[1],i=180*Math.atan2(r,e)/Math.PI;return i<C?i+360:i},M=0,N=f(w,-m,0).map(E);M<N.length;M++)(k=N[M])>C&&k<P&&h(x(n.cX,m,w,k));for(var V=0,_=f(T,-v,0).map(E);V<_.length;V++){var k;(k=_[V])>C&&k<P&&l(x(n.cY,v,T,k))}}return e}));return o.minX=1/0,o.maxX=-1/0,o.minY=1/0,o.maxY=-1/0,o}}(y||(y={}));var w,T=function(){function t(){}return t.prototype.round=function(t){return this.transform(y.ROUND(t))},t.prototype.toAbs=function(){return this.transform(y.TO_ABS())},t.prototype.toRel=function(){return this.transform(y.TO_REL())},t.prototype.normalizeHVZ=function(t,e,r){return this.transform(y.NORMALIZE_HVZ(t,e,r))},t.prototype.normalizeST=function(){return this.transform(y.NORMALIZE_ST())},t.prototype.qtToC=function(){return this.transform(y.QT_TO_C())},t.prototype.aToC=function(){return this.transform(y.A_TO_C())},t.prototype.sanitize=function(t){return this.transform(y.SANITIZE(t))},t.prototype.translate=function(t,e){return this.transform(y.TRANSLATE(t,e))},t.prototype.scale=function(t,e){return this.transform(y.SCALE(t,e))},t.prototype.rotate=function(t,e,r){return this.transform(y.ROTATE(t,e,r))},t.prototype.matrix=function(t,e,r,i,n,s){return this.transform(y.MATRIX(t,e,r,i,n,s))},t.prototype.skewX=function(t){return this.transform(y.SKEW_X(t))},t.prototype.skewY=function(t){return this.transform(y.SKEW_Y(t))},t.prototype.xSymmetry=function(t){return this.transform(y.X_AXIS_SYMMETRY(t))},t.prototype.ySymmetry=function(t){return this.transform(y.Y_AXIS_SYMMETRY(t))},t.prototype.annotateArcs=function(){return this.transform(y.ANNOTATE_ARCS())},t}(),A=function(t){return" "===t||"\t"===t||"\r"===t||"\n"===t},C=function(t){return"0".charCodeAt(0)<=t.charCodeAt(0)&&t.charCodeAt(0)<="9".charCodeAt(0)},P=function(t){function e(){var e=t.call(this)||this;return e.curNumber="",e.curCommandType=-1,e.curCommandRelative=!1,e.canParseCommandOrComma=!0,e.curNumberHasExp=!1,e.curNumberHasExpDigits=!1,e.curNumberHasDecimal=!1,e.curArgs=[],e}return u(e,t),e.prototype.finish=function(t){if(void 0===t&&(t=[]),this.parse(" ",t),0!==this.curArgs.length||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return t},e.prototype.parse=function(t,e){var r=this;void 0===e&&(e=[]);for(var i=function(t){e.push(t),r.curArgs.length=0,r.canParseCommandOrComma=!0},n=0;n<t.length;n++){var s=t[n],a=!(this.curCommandType!==O.ARC||3!==this.curArgs.length&&4!==this.curArgs.length||1!==this.curNumber.length||"0"!==this.curNumber&&"1"!==this.curNumber),o=C(s)&&("0"===this.curNumber&&"0"===s||a);if(!C(s)||o)if("e"!==s&&"E"!==s)if("-"!==s&&"+"!==s||!this.curNumberHasExp||this.curNumberHasExpDigits)if("."!==s||this.curNumberHasExp||this.curNumberHasDecimal||a){if(this.curNumber&&-1!==this.curCommandType){var h=Number(this.curNumber);if(isNaN(h))throw new SyntaxError("Invalid number ending at "+n);if(this.curCommandType===O.ARC)if(0===this.curArgs.length||1===this.curArgs.length){if(0>h)throw new SyntaxError('Expected positive number, got "'+h+'" at index "'+n+'"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&"0"!==this.curNumber&&"1"!==this.curNumber)throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+n+'"');this.curArgs.push(h),this.curArgs.length===E[this.curCommandType]&&(O.HORIZ_LINE_TO===this.curCommandType?i({type:O.HORIZ_LINE_TO,relative:this.curCommandRelative,x:h}):O.VERT_LINE_TO===this.curCommandType?i({type:O.VERT_LINE_TO,relative:this.curCommandRelative,y:h}):this.curCommandType===O.MOVE_TO||this.curCommandType===O.LINE_TO||this.curCommandType===O.SMOOTH_QUAD_TO?(i({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),O.MOVE_TO===this.curCommandType&&(this.curCommandType=O.LINE_TO)):this.curCommandType===O.CURVE_TO?i({type:O.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===O.SMOOTH_CURVE_TO?i({type:O.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===O.QUAD_TO?i({type:O.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===O.ARC&&i({type:O.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!A(s))if(","===s&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if("+"!==s&&"-"!==s&&"."!==s)if(o)this.curNumber=s,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw new SyntaxError("Unterminated command at index "+n+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+s+'" at index '+n+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,"z"!==s&&"Z"!==s)if("h"===s||"H"===s)this.curCommandType=O.HORIZ_LINE_TO,this.curCommandRelative="h"===s;else if("v"===s||"V"===s)this.curCommandType=O.VERT_LINE_TO,this.curCommandRelative="v"===s;else if("m"===s||"M"===s)this.curCommandType=O.MOVE_TO,this.curCommandRelative="m"===s;else if("l"===s||"L"===s)this.curCommandType=O.LINE_TO,this.curCommandRelative="l"===s;else if("c"===s||"C"===s)this.curCommandType=O.CURVE_TO,this.curCommandRelative="c"===s;else if("s"===s||"S"===s)this.curCommandType=O.SMOOTH_CURVE_TO,this.curCommandRelative="s"===s;else if("q"===s||"Q"===s)this.curCommandType=O.QUAD_TO,this.curCommandRelative="q"===s;else if("t"===s||"T"===s)this.curCommandType=O.SMOOTH_QUAD_TO,this.curCommandRelative="t"===s;else{if("a"!==s&&"A"!==s)throw new SyntaxError('Unexpected character "'+s+'" at index '+n+".");this.curCommandType=O.ARC,this.curCommandRelative="a"===s}else e.push({type:O.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=s,this.curNumberHasDecimal="."===s}else this.curNumber+=s,this.curNumberHasDecimal=!0;else this.curNumber+=s;else this.curNumber+=s,this.curNumberHasExp=!0;else this.curNumber+=s,this.curNumberHasExpDigits=this.curNumberHasExp}return e},e.prototype.transform=function(t){return Object.create(this,{parse:{value:function(e,r){void 0===r&&(r=[]);for(var i=0,n=Object.getPrototypeOf(this).parse.call(this,e);i<n.length;i++){var s=n[i],a=t(s);Array.isArray(a)?r.push.apply(r,a):r.push(a)}return r}}})},e}(T),O=function(t){function e(r){var i=t.call(this)||this;return i.commands="string"==typeof r?e.parse(r):r,i}return u(e,t),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var t=y.CALCULATE_BOUNDS();return this.transform(t),t},e.prototype.transform=function(t){for(var e=[],r=0,i=this.commands;r<i.length;r++){var n=t(i[r]);Array.isArray(n)?e.push.apply(e,n):e.push(n)}return this.commands=e,this},e.encode=function(t){return function(t){var e="";Array.isArray(t)||(t=[t]);for(var r=0;r<t.length;r++){var i=t[r];if(i.type===O.CLOSE_PATH)e+="z";else if(i.type===O.HORIZ_LINE_TO)e+=(i.relative?"h":"H")+i.x;else if(i.type===O.VERT_LINE_TO)e+=(i.relative?"v":"V")+i.y;else if(i.type===O.MOVE_TO)e+=(i.relative?"m":"M")+i.x+" "+i.y;else if(i.type===O.LINE_TO)e+=(i.relative?"l":"L")+i.x+" "+i.y;else if(i.type===O.CURVE_TO)e+=(i.relative?"c":"C")+i.x1+" "+i.y1+" "+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===O.SMOOTH_CURVE_TO)e+=(i.relative?"s":"S")+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===O.QUAD_TO)e+=(i.relative?"q":"Q")+i.x1+" "+i.y1+" "+i.x+" "+i.y;else if(i.type===O.SMOOTH_QUAD_TO)e+=(i.relative?"t":"T")+i.x+" "+i.y;else{if(i.type!==O.ARC)throw new Error('Unexpected command type "'+i.type+'" at index '+r+".");e+=(i.relative?"a":"A")+i.rX+" "+i.rY+" "+i.xRot+" "+ +i.lArcFlag+" "+ +i.sweepFlag+" "+i.x+" "+i.y}}return e}(t)},e.parse=function(t){var e=new P,r=[];return e.parse(t,r),e.finish(r),r},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(T),E=((w={})[O.MOVE_TO]=2,w[O.LINE_TO]=2,w[O.HORIZ_LINE_TO]=1,w[O.VERT_LINE_TO]=1,w[O.CLOSE_PATH]=0,w[O.QUAD_TO]=4,w[O.SMOOTH_QUAD_TO]=2,w[O.CURVE_TO]=6,w[O.SMOOTH_CURVE_TO]=4,w[O.ARC]=7,w);function M(t){return M="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M(t)}var N=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],V=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function _(t,e,r,i,n){if("string"===typeof t&&(t=document.getElementById(t)),!t||"object"!==M(t)||!("getContext"in t))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var s=t.getContext("2d");try{return s.getImageData(e,r,i,n)}catch(a){throw new Error("unable to access image data: "+a)}}function k(t,e,r,i,n,s){if(!(isNaN(s)||s<1)){s|=0;var a=_(t,e,r,i,n);a=function(t,e,r,i,n,s){for(var a,o=t.data,h=2*s+1,l=i-1,u=n-1,c=s+1,g=c*(c+1)/2,d=new R,p=d,f=1;f<h;f++)p=p.next=new R,f===c&&(a=p);p.next=d;for(var y=null,m=null,v=0,x=0,b=N[s],S=V[s],w=0;w<n;w++){p=d;for(var T=o[x],A=o[x+1],C=o[x+2],P=o[x+3],O=0;O<c;O++)p.r=T,p.g=A,p.b=C,p.a=P,p=p.next;for(var E=0,M=0,_=0,k=0,I=c*T,L=c*A,D=c*C,B=c*P,z=g*T,H=g*A,F=g*C,U=g*P,X=1;X<c;X++){var Y=x+((l<X?l:X)<<2),j=o[Y],q=o[Y+1],Q=o[Y+2],W=o[Y+3],G=c-X;z+=(p.r=j)*G,H+=(p.g=q)*G,F+=(p.b=Q)*G,U+=(p.a=W)*G,E+=j,M+=q,_+=Q,k+=W,p=p.next}y=d,m=a;for(var $=0;$<i;$++){var Z=U*b>>>S;if(o[x+3]=Z,0!==Z){var K=255/Z;o[x]=(z*b>>>S)*K,o[x+1]=(H*b>>>S)*K,o[x+2]=(F*b>>>S)*K}else o[x]=o[x+1]=o[x+2]=0;z-=I,H-=L,F-=D,U-=B,I-=y.r,L-=y.g,D-=y.b,B-=y.a;var J=$+s+1;J=v+(J<l?J:l)<<2,z+=E+=y.r=o[J],H+=M+=y.g=o[J+1],F+=_+=y.b=o[J+2],U+=k+=y.a=o[J+3],y=y.next;var tt=m,et=tt.r,rt=tt.g,it=tt.b,nt=tt.a;I+=et,L+=rt,D+=it,B+=nt,E-=et,M-=rt,_-=it,k-=nt,m=m.next,x+=4}v+=i}for(var st=0;st<i;st++){var at=o[x=st<<2],ot=o[x+1],ht=o[x+2],lt=o[x+3],ut=c*at,ct=c*ot,gt=c*ht,dt=c*lt,pt=g*at,ft=g*ot,yt=g*ht,mt=g*lt;p=d;for(var vt=0;vt<c;vt++)p.r=at,p.g=ot,p.b=ht,p.a=lt,p=p.next;for(var xt=i,bt=0,St=0,wt=0,Tt=0,At=1;At<=s;At++){x=xt+st<<2;var Ct=c-At;pt+=(p.r=at=o[x])*Ct,ft+=(p.g=ot=o[x+1])*Ct,yt+=(p.b=ht=o[x+2])*Ct,mt+=(p.a=lt=o[x+3])*Ct,Tt+=at,bt+=ot,St+=ht,wt+=lt,p=p.next,At<u&&(xt+=i)}x=st,y=d,m=a;for(var Pt=0;Pt<n;Pt++){var Ot=x<<2;o[Ot+3]=lt=mt*b>>>S,lt>0?(lt=255/lt,o[Ot]=(pt*b>>>S)*lt,o[Ot+1]=(ft*b>>>S)*lt,o[Ot+2]=(yt*b>>>S)*lt):o[Ot]=o[Ot+1]=o[Ot+2]=0,pt-=ut,ft-=ct,yt-=gt,mt-=dt,ut-=y.r,ct-=y.g,gt-=y.b,dt-=y.a,Ot=st+((Ot=Pt+c)<u?Ot:u)*i<<2,pt+=Tt+=y.r=o[Ot],ft+=bt+=y.g=o[Ot+1],yt+=St+=y.b=o[Ot+2],mt+=wt+=y.a=o[Ot+3],y=y.next,ut+=at=m.r,ct+=ot=m.g,gt+=ht=m.b,dt+=lt=m.a,Tt-=at,bt-=ot,St-=ht,wt-=lt,m=m.next,x+=i}}return t}(a,0,0,i,n,s),t.getContext("2d").putImageData(a,e,r)}}var R=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null};var I=Object.freeze({__proto__:null,offscreen:function(){var{DOMParser:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,createCanvas:(t,e)=>new OffscreenCanvas(t,e),createImage:t=>n((function*(){var e=yield fetch(t),r=yield e.blob();return yield createImageBitmap(r)}))()};return"undefined"===typeof DOMParser&&"undefined"!==typeof t||Reflect.deleteProperty(e,"DOMParser"),e},node:function(t){var{DOMParser:e,canvas:r,fetch:i}=t;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:i,createCanvas:r.createCanvas,createImage:r.loadImage}}});function L(t){return t.replace(/(?!\u3000)\s+/gm," ")}function D(t){return t.replace(/^[\n \t]+/,"")}function B(t){return t.replace(/[\n \t]+$/,"")}function z(t){return((t||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}var H=/^[A-Z-]+$/;function F(t){return H.test(t)?t.toLowerCase():t}function U(t){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t)||[];return e[2]||e[3]||e[4]}function X(t){if(!t.startsWith("rgb"))return t;var e=3;return t.replace(/\d+(\.\d+)?/g,((t,r)=>e--&&r?String(Math.round(parseFloat(t))):t))}var Y=/(\[[^\]]+\])/g,j=/(#[^\s+>~.[:]+)/g,q=/(\.[^\s+>~.[:]+)/g,Q=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,W=/(:[\w-]+\([^)]*\))/gi,G=/(:[^\s+>~.[:]+)/g,$=/([^\s+>~.[:]+)/g;function Z(t,e){var r=e.exec(t);return r?[t.replace(e," "),r.length]:[t,0]}function K(t){var e=[0,0,0],r=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),i=0;return[r,i]=Z(r,Y),e[1]+=i,[r,i]=Z(r,j),e[0]+=i,[r,i]=Z(r,q),e[1]+=i,[r,i]=Z(r,Q),e[2]+=i,[r,i]=Z(r,W),e[1]+=i,[r,i]=Z(r,G),e[1]+=i,r=r.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[r,i]=Z(r,$),e[2]+=i,e.join("")}var J=1e-8;function tt(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function et(t,e){return(t[0]*e[0]+t[1]*e[1])/(tt(t)*tt(e))}function rt(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(et(t,e))}function it(t){return t*t*t}function nt(t){return 3*t*t*(1-t)}function st(t){return 3*t*(1-t)*(1-t)}function at(t){return(1-t)*(1-t)*(1-t)}function ot(t){return t*t}function ht(t){return 2*t*(1-t)}function lt(t){return(1-t)*(1-t)}class ut{constructor(t,e,r){this.document=t,this.name=e,this.value=r,this.isNormalizedColor=!1}static empty(t){return new ut(t,"EMPTY","")}split(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:e,name:r}=this;return L(this.getString()).trim().split(t).map((t=>new ut(e,r,t)))}hasValue(t){var{value:e}=this;return null!==e&&""!==e&&(t||0!==e)&&"undefined"!==typeof e}isString(t){var{value:e}=this,r="string"===typeof e;return r&&t?t.test(e):r}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}setValue(t){return this.value=t,this}getValue(t){return"undefined"===typeof t||this.hasValue()?this.value:t}getNumber(t){if(!this.hasValue())return"undefined"===typeof t?0:parseFloat(t);var{value:e}=this,r=parseFloat(e);return this.isString(/%$/)&&(r/=100),r}getString(t){return"undefined"===typeof t||this.hasValue()?"undefined"===typeof this.value?"":String(this.value):String(t)}getColor(t){var e=this.getString(t);return this.isNormalizedColor||(this.isNormalizedColor=!0,e=X(e),this.value=e),e}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[r,i]="boolean"===typeof t?[void 0,t]:[t],{viewPort:n}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(n.computeSize("x"),n.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(n.computeSize("x"),n.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*n.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*n.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&i:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*n.computeSize(r);default:var s=this.getNumber();return e&&s<1?s*n.computeSize(r):s}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var t=this.getString(),e=/#([^)'"]+)/.exec(t);return e&&(e=e[1]),e||(e=t),this.document.definitions[e]}getFillStyleDefinition(t,e){var r=this.getDefinition();if(!r)return null;if("function"===typeof r.createGradient)return r.createGradient(this.document.ctx,t,e);if("function"===typeof r.createPattern){if(r.getHrefAttribute().hasValue()){var i=r.getAttribute("patternTransform");r=r.getHrefAttribute().getDefinition(),i.hasValue()&&r.getAttribute("patternTransform",!0).setValue(i.value)}return r.createPattern(this.document.ctx,t,e)}return null}getTextBaseline(){return this.hasValue()?ut.textBaselineMapping[this.getString()]:null}addOpacity(t){for(var e=this.getColor(),r=e.length,i=0,n=0;n<r&&(","===e[n]&&i++,3!==i);n++);if(t.hasValue()&&this.isString()&&3!==i){var s=new h(e);s.ok&&(s.alpha=t.getNumber(),e=s.toRGBA())}return new ut(this.document,this.name,e)}}ut.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class ct{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"===typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class gt{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[r=e,i=e]=z(t);return new gt(r,i)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[r=e,i=r]=z(t);return new gt(r,i)}static parsePath(t){for(var e=z(t),r=e.length,i=[],n=0;n<r;n+=2)i.push(new gt(e[n],e[n+1]));return i}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:r}=this,i=e*t[0]+r*t[2]+t[4],n=e*t[1]+r*t[3]+t[5];this.x=i,this.y=n}}class dt{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:r}=this,i=t.ctx.canvas;i.onclick=e,i.onmousemove=r,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:r}=this,{style:i}=t.ctx.canvas;i&&(i.cursor=""),e.forEach(((t,e)=>{for(var{run:i}=t,n=r[e];n;)i(n),n=n.parent})),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach(((r,n)=>{var{x:s,y:a}=r;!i[n]&&e.isPointInPath&&e.isPointInPath(s,a)&&(i[n]=t)}))}}checkBoundingBox(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach(((r,n)=>{var{x:s,y:a}=r;!i[n]&&e.isPointInBox(s,a)&&(i[n]=t)}))}}mapXY(t,e){for(var{window:r,ctx:i}=this.screen,n=new gt(t,e),s=i.canvas;s;)n.x-=s.offsetLeft,n.y-=s.offsetTop,s=s.offsetParent;return r.scrollX&&(n.x+=r.scrollX),r.scrollY&&(n.y+=r.scrollY),n}onClick(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:r,run(t){t.onClick&&t.onClick()}})}onMouseMove(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:r,run(t){t.onMouseMove&&t.onMouseMove()}})}}var pt="undefined"!==typeof window?window:null,ft="undefined"!==typeof fetch?fetch.bind(void 0):null;class yt{constructor(t){var{fetch:e=ft,window:r=pt}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=t,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new ct,this.mouse=new dt(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=r,this.fetch=e}wait(t){this.waits.push(t)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var t=this.waits.every((t=>t()));return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}setDefaults(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}setViewBox(t){var{document:e,ctx:r,aspectRatio:i,width:n,desiredWidth:s,height:a,desiredHeight:o,minX:h=0,minY:l=0,refX:u,refY:c,clip:g=!1,clipX:d=0,clipY:p=0}=t,f=L(i).replace(/^defer\s/,""),[y,m]=f.split(" "),v=y||"xMidYMid",x=m||"meet",b=n/s,S=a/o,w=Math.min(b,S),T=Math.max(b,S),A=s,C=o;"meet"===x&&(A*=w,C*=w),"slice"===x&&(A*=T,C*=T);var P=new ut(e,"refX",u),O=new ut(e,"refY",c),E=P.hasValue()&&O.hasValue();if(E&&r.translate(-w*P.getPixels("x"),-w*O.getPixels("y")),g){var M=w*d,N=w*p;r.beginPath(),r.moveTo(M,N),r.lineTo(n,N),r.lineTo(n,a),r.lineTo(M,a),r.closePath(),r.clip()}if(!E){var V="meet"===x&&w===S,_="slice"===x&&T===S,k="meet"===x&&w===b,R="slice"===x&&T===b;v.startsWith("xMid")&&(V||_)&&r.translate(n/2-A/2,0),v.endsWith("YMid")&&(k||R)&&r.translate(0,a/2-C/2),v.startsWith("xMax")&&(V||_)&&r.translate(n-A,0),v.endsWith("YMax")&&(k||R)&&r.translate(0,a-C)}switch(!0){case"none"===v:r.scale(b,S);break;case"meet"===x:r.scale(w,w);break;case"slice"===x:r.scale(T,T)}r.translate(-h,-l)}start(t){var{enableRedraw:e=!1,ignoreMouse:r=!1,ignoreAnimation:i=!1,ignoreDimensions:n=!1,ignoreClear:s=!1,forceRedraw:a,scaleWidth:h,scaleHeight:l,offsetX:u,offsetY:c}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:g,mouse:d}=this,p=1e3/g;if(this.frameDuration=p,this.readyPromise=new Promise((t=>{this.resolveReady=t})),this.isReady()&&this.render(t,n,s,h,l,u,c),e){var f=Date.now(),y=f,m=0,v=()=>{f=Date.now(),(m=f-y)>=p&&(y=f-m%p,this.shouldUpdate(i,a)&&(this.render(t,n,s,h,l,u,c),d.runEvents())),this.intervalId=o(v)};r||d.start(),this.intervalId=o(v)}}stop(){this.intervalId&&(o.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(t,e){if(!t){var{frameDuration:r}=this;if(this.animations.reduce(((t,e)=>e.update(r)||t),!1))return!0}return!("function"!==typeof e||!e())||(!(this.isReadyLock||!this.isReady())||!!this.mouse.hasEvents())}render(t,e,r,i,n,s,a){var{CLIENT_WIDTH:o,CLIENT_HEIGHT:h,viewPort:l,ctx:u,isFirstRender:c}=this,g=u.canvas;l.clear(),g.width&&g.height?l.setCurrent(g.width,g.height):l.setCurrent(o,h);var d=t.getStyle("width"),p=t.getStyle("height");!e&&(c||"number"!==typeof i&&"number"!==typeof n)&&(d.hasValue()&&(g.width=d.getPixels("x"),g.style&&(g.style.width="".concat(g.width,"px"))),p.hasValue()&&(g.height=p.getPixels("y"),g.style&&(g.style.height="".concat(g.height,"px"))));var f=g.clientWidth||g.width,y=g.clientHeight||g.height;if(e&&d.hasValue()&&p.hasValue()&&(f=d.getPixels("x"),y=p.getPixels("y")),l.setCurrent(f,y),"number"===typeof s&&t.getAttribute("x",!0).setValue(s),"number"===typeof a&&t.getAttribute("y",!0).setValue(a),"number"===typeof i||"number"===typeof n){var m=z(t.getAttribute("viewBox").getString()),v=0,x=0;if("number"===typeof i){var b=t.getStyle("width");b.hasValue()?v=b.getPixels("x")/i:isNaN(m[2])||(v=m[2]/i)}if("number"===typeof n){var S=t.getStyle("height");S.hasValue()?x=S.getPixels("y")/n:isNaN(m[3])||(x=m[3]/n)}v||(v=x),x||(x=v),t.getAttribute("width",!0).setValue(i),t.getAttribute("height",!0).setValue(n);var w=t.getStyle("transform",!0,!0);w.setValue("".concat(w.getString()," scale(").concat(1/v,", ").concat(1/x,")"))}r||u.clearRect(0,0,f,y),t.render(u),c&&(this.isFirstRender=!1)}}yt.defaultWindow=pt,yt.defaultFetch=ft;var{defaultFetch:mt}=yt,vt="undefined"!==typeof DOMParser?DOMParser:null;class xt{constructor(){var{fetch:t=mt,DOMParser:e=vt}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return n((function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)}))()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(r){return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent);return t}load(t){var e=this;return n((function*(){var r=yield e.fetch(t),i=yield r.text();return e.parseFromString(i)}))()}}class bt{constructor(t,e){this.type="translate",this.point=null,this.point=gt.parse(e)}apply(t){var{x:e,y:r}=this.point;t.translate(e||0,r||0)}unapply(t){var{x:e,y:r}=this.point;t.translate(-1*e||0,-1*r||0)}applyToPoint(t){var{x:e,y:r}=this.point;t.applyTransform([1,0,0,1,e||0,r||0])}}class St{constructor(t,e,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var i=z(e);this.angle=new ut(t,"angle",i[0]),this.originX=r[0],this.originY=r[1],this.cx=i[1]||0,this.cy=i[2]||0}apply(t){var{cx:e,cy:r,originX:i,originY:n,angle:s}=this,a=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(a,o),t.rotate(s.getRadians()),t.translate(-a,-o)}unapply(t){var{cx:e,cy:r,originX:i,originY:n,angle:s}=this,a=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(a,o),t.rotate(-1*s.getRadians()),t.translate(-a,-o)}applyToPoint(t){var{cx:e,cy:r,angle:i}=this,n=i.getRadians();t.applyTransform([1,0,0,1,e||0,r||0]),t.applyTransform([Math.cos(n),Math.sin(n),-Math.sin(n),Math.cos(n),0,0]),t.applyTransform([1,0,0,1,-e||0,-r||0])}}class wt{constructor(t,e,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var i=gt.parseScale(e);0!==i.x&&0!==i.y||(i.x=J,i.y=J),this.scale=i,this.originX=r[0],this.originY=r[1]}apply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,s=i.getPixels("x"),a=n.getPixels("y");t.translate(s,a),t.scale(e,r||e),t.translate(-s,-a)}unapply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,s=i.getPixels("x"),a=n.getPixels("y");t.translate(s,a),t.scale(1/e,1/r||e),t.translate(-s,-a)}applyToPoint(t){var{x:e,y:r}=this.scale;t.applyTransform([e||0,0,0,r||0,0,0])}}class Tt{constructor(t,e,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=z(e),this.originX=r[0],this.originY=r[1]}apply(t){var{originX:e,originY:r,matrix:i}=this,n=e.getPixels("x"),s=r.getPixels("y");t.translate(n,s),t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),t.translate(-n,-s)}unapply(t){var{originX:e,originY:r,matrix:i}=this,n=i[0],s=i[2],a=i[4],o=i[1],h=i[3],l=i[5],u=1/(n*(1*h-0*l)-s*(1*o-0*l)+a*(0*o-0*h)),c=e.getPixels("x"),g=r.getPixels("y");t.translate(c,g),t.transform(u*(1*h-0*l),u*(0*l-1*o),u*(0*a-1*s),u*(1*n-0*a),u*(s*l-a*h),u*(a*o-n*l)),t.translate(-c,-g)}applyToPoint(t){t.applyTransform(this.matrix)}}class At extends Tt{constructor(t,e,r){super(t,e,r),this.type="skew",this.angle=null,this.angle=new ut(t,"angle",e)}}class Ct extends At{constructor(t,e,r){super(t,e,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class Pt extends At{constructor(t,e,r){super(t,e,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}class Ot{constructor(t,e,r){this.document=t,this.transforms=[];var i=function(t){return L(t).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}(e);i.forEach((t=>{if("none"!==t){var[e,i]=function(t){var[e,r]=t.split("(");return[e.trim(),r.trim().replace(")","")]}(t),n=Ot.transformTypes[e];"undefined"!==typeof n&&this.transforms.push(new n(this.document,i,r))}}))}static fromElement(t,e){var r=e.getStyle("transform",!1,!0),[i,n=i]=e.getStyle("transform-origin",!1,!0).split(),s=[i,n];return r.hasValue()?new Ot(t,r.getString(),s):null}apply(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].apply(t)}unapply(t){for(var{transforms:e}=this,r=e.length-1;r>=0;r--)e[r].unapply(t)}applyToPoint(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].applyToPoint(t)}}Ot.transformTypes={translate:bt,rotate:St,scale:wt,matrix:Tt,skewX:Ct,skewY:Pt};class Et{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=t,this.node=e,this.captureTextNodes=r,this.attributes=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],e&&1===e.nodeType){if(Array.from(e.attributes).forEach((e=>{var r=F(e.nodeName);this.attributes[r]=new ut(t,r,e.value)})),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()){var i=this.getAttribute("style").getString().split(";").map((t=>t.trim()));i.forEach((e=>{if(e){var[r,i]=e.split(":").map((t=>t.trim()));this.styles[r]=new ut(t,r,i)}}))}var{definitions:n}=t,s=this.getAttribute("id");s.hasValue()&&(n[s.getString()]||(n[s.getString()]=this)),Array.from(e.childNodes).forEach((e=>{if(1===e.nodeType)this.addChild(e);else if(r&&(3===e.nodeType||4===e.nodeType)){var i=t.createTextNode(e);i.getText().length>0&&this.addChild(i)}}))}}getAttribute(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.attributes[t];if(!r&&e){var i=new ut(this.document,t,"");return this.attributes[t]=i,i}return r||ut.empty(this.document)}getHrefAttribute(){for(var t in this.attributes)if("href"===t||t.endsWith(":href"))return this.attributes[t];return ut.empty(this.document)}getStyle(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.styles[t];if(i)return i;var n=this.getAttribute(t);if(null!==n&&void 0!==n&&n.hasValue())return this.styles[t]=n,n;if(!r){var{parent:s}=this;if(s){var a=s.getStyle(t);if(null!==a&&void 0!==a&&a.hasValue())return a}}if(e){var o=new ut(this.document,t,"");return this.styles[t]=o,o}return i||ut.empty(this.document)}render(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){var e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){var r=this.getStyle("filter").getDefinition();r&&(this.applyEffects(t),r.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}setContext(t){}applyEffects(t){var e=Ot.fromElement(this.document,this);e&&e.apply(t);var r=this.getStyle("clip-path",!1,!0);if(r.hasValue()){var i=r.getDefinition();i&&i.apply(t)}}clearContext(t){}renderChildren(t){this.children.forEach((e=>{e.render(t)}))}addChild(t){var e=t instanceof Et?t:this.document.createElement(t);e.parent=this,Et.ignoreChildTypes.includes(e.type)||this.children.push(e)}matchesSelector(t){var e,{node:r}=this;if("function"===typeof r.matches)return r.matches(t);var i=null===(e=r.getAttribute)||void 0===e?void 0:e.call(r,"class");return!(!i||""===i)&&i.split(" ").some((e=>".".concat(e)===t))}addStylesFromStyleDefinition(){var{styles:t,stylesSpecificity:e}=this.document;for(var r in t)if(!r.startsWith("@")&&this.matchesSelector(r)){var i=t[r],n=e[r];if(i)for(var s in i){var a=this.stylesSpecificity[s];"undefined"===typeof a&&(a="000"),n>=a&&(this.styles[s]=i[s],this.stylesSpecificity[s]=n)}}}removeStyles(t,e){return e.reduce(((e,r)=>{var i=t.getStyle(r);if(!i.hasValue())return e;var n=i.getString();return i.setValue(""),[...e,[r,n]]}),[])}restoreStyles(t,e){e.forEach((e=>{var[r,i]=e;t.getStyle(r,!0).setValue(i)}))}isFirstChild(){var t;return 0===(null===(t=this.parent)||void 0===t?void 0:t.children.indexOf(this))}}Et.ignoreChildTypes=["title"];class Mt extends Et{constructor(t,e,r){super(t,e,r)}}function Nt(t){var e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function Vt(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function _t(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}class kt{constructor(t,e,r,i,n,s){var a=s?"string"===typeof s?kt.parse(s):s:{};this.fontFamily=n||a.fontFamily,this.fontSize=i||a.fontSize,this.fontStyle=t||a.fontStyle,this.fontWeight=r||a.fontWeight,this.fontVariant=e||a.fontVariant}static parse(){var t=arguments.length>1?arguments[1]:void 0,e="",r="",i="",n="",s="",a=L(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").trim().split(" "),o={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return a.forEach((t=>{switch(!0){case!o.fontStyle&&kt.styles.includes(t):"inherit"!==t&&(e=t),o.fontStyle=!0;break;case!o.fontVariant&&kt.variants.includes(t):"inherit"!==t&&(r=t),o.fontStyle=!0,o.fontVariant=!0;break;case!o.fontWeight&&kt.weights.includes(t):"inherit"!==t&&(i=t),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0;break;case!o.fontSize:"inherit"!==t&&([n]=t.split("/")),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0,o.fontSize=!0;break;default:"inherit"!==t&&(s+=t)}})),new kt(e,r,i,n,s,t)}toString(){return[Vt(this.fontStyle),this.fontVariant,_t(this.fontWeight),this.fontSize,(t=this.fontFamily,"undefined"===typeof process?t:t.trim().split(",").map(Nt).join(","))].join(" ").trim();var t}}kt.styles="normal|italic|oblique|inherit",kt.variants="normal|small-caps|inherit",kt.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class Rt{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=r,this.y2=i,this.addPoint(t,e),this.addPoint(r,i)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){"undefined"!==typeof t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),"undefined"!==typeof e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:r,x2:i,y2:n}=t;this.addPoint(e,r),this.addPoint(i,n)}}sumCubic(t,e,r,i,n){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*r+3*(1-t)*Math.pow(t,2)*i+Math.pow(t,3)*n}bezierCurveAdd(t,e,r,i,n){var s=6*e-12*r+6*i,a=-3*e+9*r-9*i+3*n,o=3*r-3*e;if(0!==a){var h=Math.pow(s,2)-4*o*a;if(!(h<0)){var l=(-s+Math.sqrt(h))/(2*a);0<l&&l<1&&(t?this.addX(this.sumCubic(l,e,r,i,n)):this.addY(this.sumCubic(l,e,r,i,n)));var u=(-s-Math.sqrt(h))/(2*a);0<u&&u<1&&(t?this.addX(this.sumCubic(u,e,r,i,n)):this.addY(this.sumCubic(u,e,r,i,n)))}}else{if(0===s)return;var c=-o/s;0<c&&c<1&&(t?this.addX(this.sumCubic(c,e,r,i,n)):this.addY(this.sumCubic(c,e,r,i,n)))}}addBezierCurve(t,e,r,i,n,s,a,o){this.addPoint(t,e),this.addPoint(a,o),this.bezierCurveAdd(!0,t,r,n,a),this.bezierCurveAdd(!1,e,i,s,o)}addQuadraticCurve(t,e,r,i,n,s){var a=t+2/3*(r-t),o=e+2/3*(i-e),h=a+1/3*(n-t),l=o+1/3*(s-e);this.addBezierCurve(t,e,a,h,o,l,n,s)}isPointInBox(t,e){var{x1:r,y1:i,x2:n,y2:s}=this;return r<=t&&t<=n&&i<=e&&e<=s}}class It extends O{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new gt(0,0),this.control=new gt(0,0),this.current=new gt(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",r=new gt(this.command[t],this.command[e]);return this.makeAbsolute(r)}getAsControlPoint(t,e){var r=this.getPoint(t,e);return this.control=r,r}getAsCurrentPoint(t,e){var r=this.getPoint(t,e);return this.current=r,r}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==O.CURVE_TO&&t!==O.SMOOTH_CURVE_TO&&t!==O.QUAD_TO&&t!==O.SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:r},control:{x:i,y:n}}=this;return new gt(2*e-i,2*r-n)}makeAbsolute(t){if(this.command.relative){var{x:e,y:r}=this.current;t.x+=e,t.y+=r}return t}addMarker(t,e,r){var{points:i,angles:n}=this;r&&n.length>0&&!n[n.length-1]&&(n[n.length-1]=i[i.length-1].angleTo(r)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,r=0;r<e;r++)if(!t[r])for(var i=r+1;i<e;i++)if(t[i]){t[r]=t[i];break}return t}}class Lt extends Et{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var t=1,e=this;e;){var r=e.getStyle("opacity",!1,!0);r.hasValue(!0)&&(t*=r.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var r=this.getStyle("fill"),i=this.getStyle("fill-opacity"),n=this.getStyle("stroke"),s=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var a=r.getFillStyleDefinition(this,i);a&&(t.fillStyle=a)}else if(r.hasValue()){"currentColor"===r.getString()&&r.setValue(this.getStyle("color").getColor());var o=r.getColor();"inherit"!==o&&(t.fillStyle="none"===o?"rgba(0,0,0,0)":o)}if(i.hasValue()){var h=new ut(this.document,"fill",t.fillStyle).addOpacity(i).getColor();t.fillStyle=h}if(n.isUrlDefinition()){var l=n.getFillStyleDefinition(this,s);l&&(t.strokeStyle=l)}else if(n.hasValue()){"currentColor"===n.getString()&&n.setValue(this.getStyle("color").getColor());var u=n.getString();"inherit"!==u&&(t.strokeStyle="none"===u?"rgba(0,0,0,0)":u)}if(s.hasValue()){var c=new ut(this.document,"stroke",t.strokeStyle).addOpacity(s).getString();t.strokeStyle=c}var g=this.getStyle("stroke-width");if(g.hasValue()){var d=g.getPixels();t.lineWidth=d||J}var p=this.getStyle("stroke-linecap"),f=this.getStyle("stroke-linejoin"),y=this.getStyle("stroke-miterlimit"),m=this.getStyle("stroke-dasharray"),v=this.getStyle("stroke-dashoffset");if(p.hasValue()&&(t.lineCap=p.getString()),f.hasValue()&&(t.lineJoin=f.getString()),y.hasValue()&&(t.miterLimit=y.getNumber()),m.hasValue()&&"none"!==m.getString()){var x=z(m.getString());"undefined"!==typeof t.setLineDash?t.setLineDash(x):"undefined"!==typeof t.webkitLineDash?t.webkitLineDash=x:"undefined"===typeof t.mozDash||1===x.length&&0===x[0]||(t.mozDash=x);var b=v.getPixels();"undefined"!==typeof t.lineDashOffset?t.lineDashOffset=b:"undefined"!==typeof t.webkitLineDashOffset?t.webkitLineDashOffset=b:"undefined"!==typeof t.mozDashOffset&&(t.mozDashOffset=b)}}if(this.modifiedEmSizeStack=!1,"undefined"!==typeof t.font){var S=this.getStyle("font"),w=this.getStyle("font-style"),T=this.getStyle("font-variant"),A=this.getStyle("font-weight"),C=this.getStyle("font-size"),P=this.getStyle("font-family"),O=new kt(w.getString(),T.getString(),A.getString(),C.hasValue()?"".concat(C.getPixels(!0),"px"):"",P.getString(),kt.parse(S.getString(),t.font));w.setValue(O.fontStyle),T.setValue(O.fontVariant),A.setValue(O.fontWeight),C.setValue(O.fontSize),P.setValue(O.fontFamily),t.font=O.toString(),C.isPixels()&&(this.document.emSize=C.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}class Dt extends Lt{constructor(t,e,r){super(t,e,r),this.type="path",this.pathParser=null,this.pathParser=new It(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,r=new Rt;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case It.MOVE_TO:this.pathM(t,r);break;case It.LINE_TO:this.pathL(t,r);break;case It.HORIZ_LINE_TO:this.pathH(t,r);break;case It.VERT_LINE_TO:this.pathV(t,r);break;case It.CURVE_TO:this.pathC(t,r);break;case It.SMOOTH_CURVE_TO:this.pathS(t,r);break;case It.QUAD_TO:this.pathQ(t,r);break;case It.SMOOTH_QUAD_TO:this.pathT(t,r);break;case It.ARC:this.pathA(t,r);break;case It.CLOSE_PATH:this.pathZ(t,r)}return r}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),r=t.getMarkerAngles(),i=e.map(((t,e)=>[t,r[e]]));return i}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var r=this.getMarkers();if(r){var i=r.length-1,n=this.getStyle("marker-start"),s=this.getStyle("marker-mid"),a=this.getStyle("marker-end");if(n.isUrlDefinition()){var o=n.getDefinition(),[h,l]=r[0];o.render(t,h,l)}if(s.isUrlDefinition())for(var u=s.getDefinition(),c=1;c<i;c++){var[g,d]=r[c];u.render(t,g,d)}if(a.isUrlDefinition()){var p=a.getDefinition(),[f,y]=r[i];p.render(t,f,y)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:r}=this,{point:i}=Dt.pathM(r),{x:n,y:s}=i;r.addMarker(i),e.addPoint(n,s),t&&t.moveTo(n,s)}static pathL(t){var{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){var{pathParser:r}=this,{current:i,point:n}=Dt.pathL(r),{x:s,y:a}=n;r.addMarker(n,i),e.addPoint(s,a),t&&t.lineTo(s,a)}static pathH(t){var{current:e,command:r}=t,i=new gt((r.relative?e.x:0)+r.x,e.y);return t.current=i,{current:e,point:i}}pathH(t,e){var{pathParser:r}=this,{current:i,point:n}=Dt.pathH(r),{x:s,y:a}=n;r.addMarker(n,i),e.addPoint(s,a),t&&t.lineTo(s,a)}static pathV(t){var{current:e,command:r}=t,i=new gt(e.x,(r.relative?e.y:0)+r.y);return t.current=i,{current:e,point:i}}pathV(t,e){var{pathParser:r}=this,{current:i,point:n}=Dt.pathV(r),{x:s,y:a}=n;r.addMarker(n,i),e.addPoint(s,a),t&&t.lineTo(s,a)}static pathC(t){var{current:e}=t;return{current:e,point:t.getPoint("x1","y1"),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:s,currentPoint:a}=Dt.pathC(r);r.addMarker(a,s,n),e.addBezierCurve(i.x,i.y,n.x,n.y,s.x,s.y,a.x,a.y),t&&t.bezierCurveTo(n.x,n.y,s.x,s.y,a.x,a.y)}static pathS(t){var{current:e}=t;return{current:e,point:t.getReflectedControlPoint(),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:s,currentPoint:a}=Dt.pathS(r);r.addMarker(a,s,n),e.addBezierCurve(i.x,i.y,n.x,n.y,s.x,s.y,a.x,a.y),t&&t.bezierCurveTo(n.x,n.y,s.x,s.y,a.x,a.y)}static pathQ(t){var{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:s}=Dt.pathQ(r);r.addMarker(s,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,s.x,s.y),t&&t.quadraticCurveTo(n.x,n.y,s.x,s.y)}static pathT(t){var{current:e}=t,r=t.getReflectedControlPoint();return t.control=r,{current:e,controlPoint:r,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:s}=Dt.pathT(r);r.addMarker(s,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,s.x,s.y),t&&t.quadraticCurveTo(n.x,n.y,s.x,s.y)}static pathA(t){var{current:e,command:r}=t,{rX:i,rY:n,xRot:s,lArcFlag:a,sweepFlag:o}=r,h=s*(Math.PI/180),l=t.getAsCurrentPoint(),u=new gt(Math.cos(h)*(e.x-l.x)/2+Math.sin(h)*(e.y-l.y)/2,-Math.sin(h)*(e.x-l.x)/2+Math.cos(h)*(e.y-l.y)/2),c=Math.pow(u.x,2)/Math.pow(i,2)+Math.pow(u.y,2)/Math.pow(n,2);c>1&&(i*=Math.sqrt(c),n*=Math.sqrt(c));var g=(a===o?-1:1)*Math.sqrt((Math.pow(i,2)*Math.pow(n,2)-Math.pow(i,2)*Math.pow(u.y,2)-Math.pow(n,2)*Math.pow(u.x,2))/(Math.pow(i,2)*Math.pow(u.y,2)+Math.pow(n,2)*Math.pow(u.x,2)));isNaN(g)&&(g=0);var d=new gt(g*i*u.y/n,g*-n*u.x/i),p=new gt((e.x+l.x)/2+Math.cos(h)*d.x-Math.sin(h)*d.y,(e.y+l.y)/2+Math.sin(h)*d.x+Math.cos(h)*d.y),f=rt([1,0],[(u.x-d.x)/i,(u.y-d.y)/n]),y=[(u.x-d.x)/i,(u.y-d.y)/n],m=[(-u.x-d.x)/i,(-u.y-d.y)/n],v=rt(y,m);return et(y,m)<=-1&&(v=Math.PI),et(y,m)>=1&&(v=0),{currentPoint:l,rX:i,rY:n,sweepFlag:o,xAxisRotation:h,centp:p,a1:f,ad:v}}pathA(t,e){var{pathParser:r}=this,{currentPoint:i,rX:n,rY:s,sweepFlag:a,xAxisRotation:o,centp:h,a1:l,ad:u}=Dt.pathA(r),c=1-a?1:-1,g=l+c*(u/2),d=new gt(h.x+n*Math.cos(g),h.y+s*Math.sin(g));if(r.addMarkerAngle(d,g-c*Math.PI/2),r.addMarkerAngle(i,g-c*Math.PI),e.addPoint(i.x,i.y),t&&!isNaN(l)&&!isNaN(u)){var p=n>s?n:s,f=n>s?1:n/s,y=n>s?s/n:1;t.translate(h.x,h.y),t.rotate(o),t.scale(f,y),t.arc(0,0,p,l,l+u,Boolean(1-a)),t.scale(1/f,1/y),t.rotate(-o),t.translate(-h.x,-h.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){Dt.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}class Bt extends Dt{constructor(t,e,r){super(t,e,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class zt extends Lt{constructor(t,e,r){super(t,e,new.target===zt||r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(t,e);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(t.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach(((r,i)=>{var n=this.getChildBoundingBox(t,this,this,i);e?e.addBoundingBox(n):e=n})),e}getFontSize(){var{document:t,parent:e}=this,r=kt.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(r)}getTElementBoundingBox(t){var e=this.getFontSize();return new Rt(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,r){var i=e[r],n=null;if(t.isArabic){var s=e.length,a=e[r-1],o=e[r+1],h="isolated";if((0===r||" "===a)&&r<s-1&&" "!==o&&(h="terminal"),r>0&&" "!==a&&r<s-1&&" "!==o&&(h="medial"),r>0&&" "!==a&&(r===s-1||" "===o)&&(h="initial"),"undefined"!==typeof t.glyphs[i]){var l=t.glyphs[i];n=l instanceof Bt?l:l[h]}}else n=t.glyphs[i];return n||(n=t.missingGlyph),n}getText(){return""}getTextFromNode(t){var e=t||this.node,r=Array.from(e.parentNode.childNodes),i=r.indexOf(e),n=r.length-1,s=L(e.textContent||"");return 0===i&&(s=D(s)),i===n&&(s=B(s)),s}renderChildren(t){if("text"===this.type){this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach(((e,r)=>{this.renderChild(t,this,this,r)}));var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}else this.renderTElementChildren(t)}renderTElementChildren(t){var{document:e,parent:r}=this,i=this.getText(),n=r.getStyle("font-family").getDefinition();if(n)for(var{unitsPerEm:s}=n.fontFace,a=kt.parse(e.ctx.font),o=r.getStyle("font-size").getNumber(a.fontSize),h=r.getStyle("font-style").getString(a.fontStyle),l=o/s,u=n.isRTL?i.split("").reverse().join(""):i,c=z(r.getAttribute("dx").getString()),g=u.length,d=0;d<g;d++){var p=this.getGlyph(n,u,d);t.translate(this.x,this.y),t.scale(l,-l);var f=t.lineWidth;t.lineWidth=t.lineWidth*s/o,"italic"===h&&t.transform(1,0,.4,1,0,0),p.render(t),"italic"===h&&t.transform(1,0,-.4,1,0,0),t.lineWidth=f,t.scale(1/l,-1/l),t.translate(-this.x,-this.y),this.x+=o*(p.horizAdvX||n.horizAdvX)/s,"undefined"===typeof c[d]||isNaN(c[d])||(this.x+=c[d])}else{var{x:y,y:m}=this;t.fillStyle&&t.fillText(i,y,m),t.strokeStyle&&t.strokeText(i,y,m)}}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start"),r=0;r="start"===e?t.x-this.minX:"end"===e?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(var i=this.textChunkStart;i<this.leafTexts.length;i++)this.leafTexts[i].x+=r;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach(((e,r)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,r)})),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,r,i){var n=r.children[i];n.children.length>0?n.children.forEach(((r,i)=>{e.adjustChildCoordinatesRecursiveCore(t,e,n,i)})):this.adjustChildCoordinates(t,e,r,i)}adjustChildCoordinates(t,e,r,i){var n=r.children[i];if("function"!==typeof n.measureText)return n;t.save(),n.setContext(t,!0);var s=n.getAttribute("x"),a=n.getAttribute("y"),o=n.getAttribute("dx"),h=n.getAttribute("dy"),l=n.getStyle("font-family").getDefinition(),u=Boolean(l)&&l.isRTL;0===i&&(s.hasValue()||s.setValue(n.getInheritedAttribute("x")),a.hasValue()||a.setValue(n.getInheritedAttribute("y")),o.hasValue()||o.setValue(n.getInheritedAttribute("dx")),h.hasValue()||h.setValue(n.getInheritedAttribute("dy")));var c=n.measureText(t);return u&&(e.x-=c),s.hasValue()?(e.applyAnchoring(),n.x=s.getPixels("x"),o.hasValue()&&(n.x+=o.getPixels("x"))):(o.hasValue()&&(e.x+=o.getPixels("x")),n.x=e.x),e.x=n.x,u||(e.x+=c),a.hasValue()?(n.y=a.getPixels("y"),h.hasValue()&&(n.y+=h.getPixels("y"))):(h.hasValue()&&(e.y+=h.getPixels("y")),n.y=e.y),e.y=n.y,e.leafTexts.push(n),e.minX=Math.min(e.minX,n.x,n.x+c),e.maxX=Math.max(e.maxX,n.x,n.x+c),n.clearContext(t),t.restore(),n}getChildBoundingBox(t,e,r,i){var n=r.children[i];if("function"!==typeof n.getBoundingBox)return null;var s=n.getBoundingBox(t);return s?(n.children.forEach(((r,i)=>{var a=e.getChildBoundingBox(t,e,n,i);s.addBoundingBox(a)})),s):null}renderChild(t,e,r,i){var n=r.children[i];n.render(t),n.children.forEach(((r,i)=>{e.renderChild(t,e,n,i)}))}measureText(t){var{measureCache:e}=this;if(~e)return e;var r=this.getText(),i=this.measureTargetText(t,r);return this.measureCache=i,i}measureTargetText(t,e){if(!e.length)return 0;var{parent:r}=this,i=r.getStyle("font-family").getDefinition();if(i){for(var n=this.getFontSize(),s=i.isRTL?e.split("").reverse().join(""):e,a=z(r.getAttribute("dx").getString()),o=s.length,h=0,l=0;l<o;l++){h+=(this.getGlyph(i,s,l).horizAdvX||i.horizAdvX)*n/i.fontFace.unitsPerEm,"undefined"===typeof a[l]||isNaN(a[l])||(h+=a[l])}return h}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:u}=t.measureText(e);return this.clearContext(t),t.restore(),u}getInheritedAttribute(t){for(var e=this;e instanceof zt&&e.isFirstChild();){var r=e.parent.getAttribute(t);if(r.hasValue(!0))return r.getValue("0");e=e.parent}return null}}class Ht extends zt{constructor(t,e,r){super(t,e,new.target===Ht||r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class Ft extends Ht{constructor(){super(...arguments),this.type="textNode"}}class Ut extends Lt{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:r}=this,{screen:i,window:n}=r,s=t.canvas;if(i.setDefaults(t),s.style&&"undefined"!==typeof t.font&&n&&"undefined"!==typeof n.getComputedStyle){t.font=n.getComputedStyle(s).getPropertyValue("font");var a=new ut(r,"fontSize",kt.parse(t.font).fontSize);a.hasValue()&&(r.rootEmSize=a.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:o,height:h}=i.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var l=this.getAttribute("refX"),u=this.getAttribute("refY"),c=this.getAttribute("viewBox"),g=c.hasValue()?z(c.getString()):null,d=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),p=0,f=0,y=0,m=0;g&&(p=g[0],f=g[1]),this.root||(o=this.getStyle("width").getPixels("x"),h=this.getStyle("height").getPixels("y"),"marker"===this.type&&(y=p,m=f,p=0,f=0)),i.viewPort.setCurrent(o,h),!this.node||this.parent&&"foreignObject"!==(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName)||!this.getStyle("transform",!1,!0).hasValue()||this.getStyle("transform-origin",!1,!0).hasValue()||this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),g&&(o=g[2],h=g[3]),r.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:i.viewPort.width,desiredWidth:o,height:i.viewPort.height,desiredHeight:h,minX:p,minY:f,refX:l.getValue(),refY:u.getValue(),clip:d,clipX:y,clipY:m}),g&&(i.viewPort.removeCurrent(),i.viewPort.setCurrent(o,h))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.getAttribute("width",!0),n=this.getAttribute("height",!0),s=this.getAttribute("viewBox"),a=this.getAttribute("style"),o=i.getNumber(0),h=n.getNumber(0);if(r)if("string"===typeof r)this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var l=this.getAttribute("preserveAspectRatio");l.hasValue()&&l.setValue(l.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(i.setValue(t),n.setValue(e),s.hasValue()||s.setValue("0 0 ".concat(o||t," ").concat(h||e)),a.hasValue()){var u=this.getStyle("width"),c=this.getStyle("height");u.hasValue()&&u.setValue("".concat(t,"px")),c.hasValue()&&c.setValue("".concat(e,"px"))}}}class Xt extends Dt{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),i=this.getStyle("width",!1,!0).getPixels("x"),n=this.getStyle("height",!1,!0).getPixels("y"),s=this.getAttribute("rx"),a=this.getAttribute("ry"),o=s.getPixels("x"),h=a.getPixels("y");if(s.hasValue()&&!a.hasValue()&&(h=o),a.hasValue()&&!s.hasValue()&&(o=h),o=Math.min(o,i/2),h=Math.min(h,n/2),t){var l=(Math.sqrt(2)-1)/3*4;t.beginPath(),n>0&&i>0&&(t.moveTo(e+o,r),t.lineTo(e+i-o,r),t.bezierCurveTo(e+i-o+l*o,r,e+i,r+h-l*h,e+i,r+h),t.lineTo(e+i,r+n-h),t.bezierCurveTo(e+i,r+n-h+l*h,e+i-o+l*o,r+n,e+i-o,r+n),t.lineTo(e+o,r+n),t.bezierCurveTo(e+o-l*o,r+n,e,r+n-h+l*h,e,r+n-h),t.lineTo(e,r+h),t.bezierCurveTo(e,r+h-l*h,e+o-l*o,r,e+o,r),t.closePath())}return new Rt(e,r,e+i,r+n)}getMarkers(){return null}}class Yt extends Dt{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),i=this.getAttribute("r").getPixels();return t&&i>0&&(t.beginPath(),t.arc(e,r,i,0,2*Math.PI,!1),t.closePath()),new Rt(e-i,r-i,e+i,r+i)}getMarkers(){return null}}class jt extends Dt{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,r=this.getAttribute("rx").getPixels("x"),i=this.getAttribute("ry").getPixels("y"),n=this.getAttribute("cx").getPixels("x"),s=this.getAttribute("cy").getPixels("y");return t&&r>0&&i>0&&(t.beginPath(),t.moveTo(n+r,s),t.bezierCurveTo(n+r,s+e*i,n+e*r,s+i,n,s+i),t.bezierCurveTo(n-e*r,s+i,n-r,s+e*i,n-r,s),t.bezierCurveTo(n-r,s-e*i,n-e*r,s-i,n,s-i),t.bezierCurveTo(n+e*r,s-i,n+r,s-e*i,n+r,s),t.closePath()),new Rt(n-r,s-i,n+r,s+i)}getMarkers(){return null}}class qt extends Dt{constructor(){super(...arguments),this.type="line"}getPoints(){return[new gt(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new gt(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:r},{x:i,y:n}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,r),t.lineTo(i,n)),new Rt(e,r,i,n)}getMarkers(){var[t,e]=this.getPoints(),r=t.angleTo(e);return[[t,r],[e,r]]}}class Qt extends Dt{constructor(t,e,r){super(t,e,r),this.type="polyline",this.points=[],this.points=gt.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:r,y:i}]=e,n=new Rt(r,i);return t&&(t.beginPath(),t.moveTo(r,i)),e.forEach((e=>{var{x:r,y:i}=e;n.addPoint(r,i),t&&t.lineTo(r,i)})),n}getMarkers(){var{points:t}=this,e=t.length-1,r=[];return t.forEach(((i,n)=>{n!==e&&r.push([i,i.angleTo(t[n+1])])})),r.length>0&&r.push([t[t.length-1],r[r.length-1][1]]),r}}class Wt extends Qt{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:r,y:i}]=this.points;return t&&(t.lineTo(r,i),t.closePath()),e}}class Gt extends Et{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,r){var i=this.getStyle("width").getPixels("x",!0),n=this.getStyle("height").getPixels("y",!0),s=new Ut(this.document,null);s.attributes.viewBox=new ut(this.document,"viewBox",this.getAttribute("viewBox").getValue()),s.attributes.width=new ut(this.document,"width","".concat(i,"px")),s.attributes.height=new ut(this.document,"height","".concat(n,"px")),s.attributes.transform=new ut(this.document,"transform",this.getAttribute("patternTransform").getValue()),s.children=this.children;var a=this.document.createCanvas(i,n),o=a.getContext("2d"),h=this.getAttribute("x"),l=this.getAttribute("y");h.hasValue()&&l.hasValue()&&o.translate(h.getPixels("x",!0),l.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var u=-1;u<=1;u++)for(var c=-1;c<=1;c++)o.save(),s.attributes.x=new ut(this.document,"x",u*a.width),s.attributes.y=new ut(this.document,"y",c*a.height),s.render(o),o.restore();return t.createPattern(a,"repeat")}}class $t extends Et{constructor(){super(...arguments),this.type="marker"}render(t,e,r){if(e){var{x:i,y:n}=e,s=this.getAttribute("orient").getString("auto"),a=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(i,n),"auto"===s&&t.rotate(r),"strokeWidth"===a&&t.scale(t.lineWidth,t.lineWidth),t.save();var o=new Ut(this.document,null);o.type=this.type,o.attributes.viewBox=new ut(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.refX=new ut(this.document,"refX",this.getAttribute("refX").getValue()),o.attributes.refY=new ut(this.document,"refY",this.getAttribute("refY").getValue()),o.attributes.width=new ut(this.document,"width",this.getAttribute("markerWidth").getValue()),o.attributes.height=new ut(this.document,"height",this.getAttribute("markerHeight").getValue()),o.attributes.overflow=new ut(this.document,"overflow",this.getAttribute("overflow").getValue()),o.attributes.fill=new ut(this.document,"fill",this.getAttribute("fill").getColor("black")),o.attributes.stroke=new ut(this.document,"stroke",this.getAttribute("stroke").getValue("none")),o.children=this.children,o.render(t),t.restore(),"strokeWidth"===a&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===s&&t.rotate(-r),t.translate(-i,-n)}}}class Zt extends Et{constructor(){super(...arguments),this.type="defs"}render(){}}class Kt extends Lt{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new Rt;return this.children.forEach((r=>{e.addBoundingBox(r.getBoundingBox(t))})),e}}class Jt extends Et{constructor(t,e,r){super(t,e,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:i,children:n}=this;n.forEach((t=>{"stop"===t.type&&i.push(t)}))}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,r){var i=this;this.getHrefAttribute().hasValue()&&(i=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(i));var{stops:n}=i,s=this.getGradient(t,e);if(!s)return this.addParentOpacity(r,n[n.length-1].color);if(n.forEach((t=>{s.addColorStop(t.offset,this.addParentOpacity(r,t.color))})),this.getAttribute("gradientTransform").hasValue()){var{document:a}=this,{MAX_VIRTUAL_PIXELS:o,viewPort:h}=a.screen,[l]=h.viewPorts,u=new Xt(a,null);u.attributes.x=new ut(a,"x",-o/3),u.attributes.y=new ut(a,"y",-o/3),u.attributes.width=new ut(a,"width",o),u.attributes.height=new ut(a,"height",o);var c=new Kt(a,null);c.attributes.transform=new ut(a,"transform",this.getAttribute("gradientTransform").getValue()),c.children=[u];var g=new Ut(a,null);g.attributes.x=new ut(a,"x",0),g.attributes.y=new ut(a,"y",0),g.attributes.width=new ut(a,"width",l.width),g.attributes.height=new ut(a,"height",l.height),g.children=[c];var d=a.createCanvas(l.width,l.height),p=d.getContext("2d");return p.fillStyle=s,g.render(p),p.createPattern(d,"no-repeat")}return s}inheritStopContainer(t){this.attributesToInherit.forEach((e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())}))}addParentOpacity(t,e){return t.hasValue()?new ut(this.document,"color",e).addOpacity(t).getColor():e}}class te extends Jt{constructor(t,e,r){super(t,e,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=r?e.getBoundingBox(t):null;if(r&&!i)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var n=r?i.x+i.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),s=r?i.y+i.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),a=r?i.x+i.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),o=r?i.y+i.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return n===a&&s===o?null:t.createLinearGradient(n,s,a,o)}}class ee extends Jt{constructor(t,e,r){super(t,e,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=e.getBoundingBox(t);if(r&&!i)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var n=r?i.x+i.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),s=r?i.y+i.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),a=n,o=s;this.getAttribute("fx").hasValue()&&(a=r?i.x+i.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(o=r?i.y+i.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var h=r?(i.width+i.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),l=this.getAttribute("fr").getPixels();return t.createRadialGradient(a,o,l,n,s,h)}}class re extends Et{constructor(t,e,r){super(t,e,r),this.type="stop";var i=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),n=this.getStyle("stop-opacity"),s=this.getStyle("stop-color",!0);""===s.getString()&&s.setValue("#000"),n.hasValue()&&(s=s.addOpacity(n)),this.offset=i,this.color=s.getColor()}}class ie extends Et{constructor(t,e,r){super(t,e,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new ut(t,"values",null);var i=this.getAttribute("values");i.hasValue()&&this.values.setValue(i.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:r,to:i}=this.getProgress(),n=r.getNumber()+(i.getNumber()-r.getNumber())*e;return"%"===t&&(n*=100),"".concat(n).concat(t)}update(t){var{parent:e}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var i=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==i||this.frozen){if("remove"===i&&!this.removed)return this.removed=!0,r.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=r.getString();return!1}this.duration+=t;var n=!1;if(this.begin<this.duration){var s=this.calcValue(),a=this.getAttribute("type");if(a.hasValue()){var o=a.getString();s="".concat(o,"(").concat(s,")")}r.setValue(s),n=!0}return n}getProgress(){var{document:t,values:e}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var i=r.progress*(e.getValue().length-1),n=Math.floor(i),s=Math.ceil(i);r.from=new ut(t,"from",parseFloat(e.getValue()[n])),r.to=new ut(t,"to",parseFloat(e.getValue()[s])),r.progress=(i-n)/(s-n)}else r.from=this.from,r.to=this.to;return r}}class ne extends ie{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=new h(e.getColor()),n=new h(r.getColor());if(i.ok&&n.ok){var s=i.r+(n.r-i.r)*t,a=i.g+(n.g-i.g)*t,o=i.b+(n.b-i.b)*t;return"rgb(".concat(Math.floor(s),", ").concat(Math.floor(a),", ").concat(Math.floor(o),")")}return this.getAttribute("from").getColor()}}class se extends ie{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=z(e.getString()),n=z(r.getString()),s=i.map(((e,r)=>e+(n[r]-e)*t)).join(" ");return s}}class ae extends Et{constructor(t,e,r){super(t,e,r),this.type="font",this.glyphs=Object.create(null),this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:i}=t,{children:n}=this;for(var s of n)switch(s.type){case"font-face":this.fontFace=s;var a=s.getStyle("font-family");a.hasValue()&&(i[a.getString()]=this);break;case"missing-glyph":this.missingGlyph=s;break;case"glyph":var o=s;o.arabicForm?(this.isRTL=!0,this.isArabic=!0,"undefined"===typeof this.glyphs[o.unicode]&&(this.glyphs[o.unicode]=Object.create(null)),this.glyphs[o.unicode][o.arabicForm]=o):this.glyphs[o.unicode]=o}}render(){}}class oe extends Et{constructor(t,e,r){super(t,e,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class he extends Dt{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class le extends zt{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}class ue extends zt{constructor(t,e,r){super(t,e,r),this.type="a";var{childNodes:i}=e,n=i[0],s=i.length>0&&Array.from(i).every((t=>3===t.nodeType));this.hasText=s,this.text=s?this.getTextFromNode(n):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:r,y:i}=this,{mouse:n}=e.screen,s=new ut(e,"fontSize",kt.parse(e.ctx.font).fontSize);n.isWorking()&&n.checkBoundingBox(this,new Rt(r,i-s.getPixels("y"),r+this.measureText(t),i))}else if(this.children.length>0){var a=new Kt(this.document,null);a.children=this.children,a.parent=this,a.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function ce(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function ge(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ce(Object(r),!0).forEach((function(e){a(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ce(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}class de extends zt{constructor(t,e,r){super(t,e,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var i=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(i)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach((e=>{var{type:r,points:i}=e;switch(r){case It.LINE_TO:t&&t.lineTo(i[0],i[1]);break;case It.MOVE_TO:t&&t.moveTo(i[0],i[1]);break;case It.CURVE_TO:t&&t.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);break;case It.QUAD_TO:t&&t.quadraticCurveTo(i[0],i[1],i[2],i[3]);break;case It.ARC:var[n,s,a,o,h,l,u,c]=i,g=a>o?a:o,d=a>o?1:a/o,p=a>o?o/a:1;t&&(t.translate(n,s),t.rotate(u),t.scale(d,p),t.arc(0,0,g,h,h+l,Boolean(1-c)),t.scale(1/d,1/p),t.rotate(-u),t.translate(-n,-s));break;case It.CLOSE_PATH:t&&t.closePath()}}))}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:i}=this,n=t.fillStyle;"underline"===e&&t.beginPath(),i.forEach(((i,n)=>{var{p0:s,p1:a,rotation:o,text:h}=i;t.save(),t.translate(s.x,s.y),t.rotate(o),t.fillStyle&&t.fillText(h,0,0),t.strokeStyle&&t.strokeText(h,0,0),t.restore(),"underline"===e&&(0===n&&t.moveTo(s.x,s.y+r/8),t.lineTo(a.x,a.y+r/5))})),"underline"===e&&(t.lineWidth=r/20,t.strokeStyle=n,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}findSegmentToFitChar(t,e,r,i,n,s,a,o,h){var l=s,u=this.measureText(t,o);" "===o&&"justify"===e&&r<i&&(u+=(i-r)/n),h>-1&&(l+=this.getLetterSpacingAt(h));var c=this.textHeight/20,g=this.getEquidistantPointOnPath(l,c,0),d=this.getEquidistantPointOnPath(l+u,c,0),p={p0:g,p1:d},f=g&&d?Math.atan2(d.y-g.y,d.x-g.x):0;if(a){var y=Math.cos(Math.PI/2+f)*a,m=Math.cos(-f)*a;p.p0=ge(ge({},g),{},{x:g.x+y,y:g.y+m}),p.p1=ge(ge({},d),{},{x:d.x+y,y:d.y+m})}return{offset:l+=u,segment:p,rotation:f}}measureText(t,e){var{measuresCache:r}=this,i=e||this.getText();if(r.has(i))return r.get(i);var n=this.measureTargetText(t,i);return r.set(i,n),n}setTextData(t){if(!this.glyphInfo){var e=this.getText(),r=e.split(""),i=e.split(" ").length-1,n=this.parent.getAttribute("dx").split().map((t=>t.getPixels("x"))),s=this.parent.getAttribute("dy").getPixels("y"),a=this.parent.getStyle("text-anchor").getString("start"),o=this.getStyle("letter-spacing"),h=this.parent.getStyle("letter-spacing"),l=0;o.hasValue()&&"inherit"!==o.getValue()?o.hasValue()&&"initial"!==o.getValue()&&"unset"!==o.getValue()&&(l=o.getPixels()):l=h.getPixels();var u=[],c=e.length;this.letterSpacingCache=u;for(var g=0;g<c;g++)u.push("undefined"!==typeof n[g]?n[g]:l);var d=u.reduce(((t,e,r)=>0===r?0:t+e||0),0),p=this.measureText(t),f=Math.max(p+d,0);this.textWidth=p,this.textHeight=this.getFontSize(),this.glyphInfo=[];var y=this.getPathLength(),m=this.getStyle("startOffset").getNumber(0)*y,v=0;"middle"!==a&&"center"!==a||(v=-f/2),"end"!==a&&"right"!==a||(v=-f),v+=m,r.forEach(((e,n)=>{var{offset:o,segment:h,rotation:l}=this.findSegmentToFitChar(t,a,f,y,i,v,s,e,n);v=o,h.p0&&h.p1&&this.glyphInfo.push({text:r[n],p0:h.p0,p1:h.p1,rotation:l})}))}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:r}=t;for(r.reset();!r.isEnd();){var{current:i}=r,n=i?i.x:0,s=i?i.y:0,a=r.next(),o=a.type,h=[];switch(a.type){case It.MOVE_TO:this.pathM(r,h);break;case It.LINE_TO:o=this.pathL(r,h);break;case It.HORIZ_LINE_TO:o=this.pathH(r,h);break;case It.VERT_LINE_TO:o=this.pathV(r,h);break;case It.CURVE_TO:this.pathC(r,h);break;case It.SMOOTH_CURVE_TO:o=this.pathS(r,h);break;case It.QUAD_TO:this.pathQ(r,h);break;case It.SMOOTH_QUAD_TO:o=this.pathT(r,h);break;case It.ARC:h=this.pathA(r);break;case It.CLOSE_PATH:Dt.pathZ(r)}a.type!==It.CLOSE_PATH?e.push({type:o,points:h,start:{x:n,y:s},pathLength:this.calcLength(n,s,o,h)}):e.push({type:It.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:r,y:i}=Dt.pathM(t).point;e.push(r,i)}pathL(t,e){var{x:r,y:i}=Dt.pathL(t).point;return e.push(r,i),It.LINE_TO}pathH(t,e){var{x:r,y:i}=Dt.pathH(t).point;return e.push(r,i),It.LINE_TO}pathV(t,e){var{x:r,y:i}=Dt.pathV(t).point;return e.push(r,i),It.LINE_TO}pathC(t,e){var{point:r,controlPoint:i,currentPoint:n}=Dt.pathC(t);e.push(r.x,r.y,i.x,i.y,n.x,n.y)}pathS(t,e){var{point:r,controlPoint:i,currentPoint:n}=Dt.pathS(t);return e.push(r.x,r.y,i.x,i.y,n.x,n.y),It.CURVE_TO}pathQ(t,e){var{controlPoint:r,currentPoint:i}=Dt.pathQ(t);e.push(r.x,r.y,i.x,i.y)}pathT(t,e){var{controlPoint:r,currentPoint:i}=Dt.pathT(t);return e.push(r.x,r.y,i.x,i.y),It.QUAD_TO}pathA(t){var{rX:e,rY:r,sweepFlag:i,xAxisRotation:n,centp:s,a1:a,ad:o}=Dt.pathA(t);return 0===i&&o>0&&(o-=2*Math.PI),1===i&&o<0&&(o+=2*Math.PI),[s.x,s.y,e,r,a,o,n,i]}calcLength(t,e,r,i){var n=0,s=null,a=null,o=0;switch(r){case It.LINE_TO:return this.getLineLength(t,e,i[0],i[1]);case It.CURVE_TO:for(n=0,s=this.getPointOnCubicBezier(0,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),o=.01;o<=1;o+=.01)a=this.getPointOnCubicBezier(o,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;return n;case It.QUAD_TO:for(n=0,s=this.getPointOnQuadraticBezier(0,t,e,i[0],i[1],i[2],i[3]),o=.01;o<=1;o+=.01)a=this.getPointOnQuadraticBezier(o,t,e,i[0],i[1],i[2],i[3]),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;return n;case It.ARC:n=0;var h=i[4],l=i[5],u=i[4]+l,c=Math.PI/180;if(Math.abs(h-u)<c&&(c=Math.abs(h-u)),s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],h,0),l<0)for(o=h-c;o>u;o-=c)a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;else for(o=h+c;o<u;o+=c)a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;return a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],u,0),n+=this.getLineLength(s.x,s.y,a.x,a.y)}return 0}getPointOnLine(t,e,r,i,n){var s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:r,o=(n-r)/(i-e+J),h=Math.sqrt(t*t/(1+o*o));i<e&&(h*=-1);var l=o*h,u=null;if(i===e)u={x:s,y:a+l};else if((a-r)/(s-e+J)===o)u={x:s+h,y:a+l};else{var c,g,d=this.getLineLength(e,r,i,n);if(d<J)return null;var p=(s-e)*(i-e)+(a-r)*(n-r);c=e+(p/=d*d)*(i-e),g=r+p*(n-r);var f=this.getLineLength(s,a,c,g),y=Math.sqrt(t*t-f*f);h=Math.sqrt(y*y/(1+o*o)),i<e&&(h*=-1),u={x:c+h,y:g+(l=o*h)}}return u}getPointOnPath(t){var e=this.getPathLength(),r=0,i=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:n}=this;for(var s of n){if(!s||!(s.pathLength<5e-5||r+s.pathLength+5e-5<t)){var a=t-r,o=0;switch(s.type){case It.LINE_TO:i=this.getPointOnLine(a,s.start.x,s.start.y,s.points[0],s.points[1],s.start.x,s.start.y);break;case It.ARC:var h=s.points[4],l=s.points[5],u=s.points[4]+l;if(o=h+a/s.pathLength*l,l<0&&o<u||l>=0&&o>u)break;i=this.getPointOnEllipticalArc(s.points[0],s.points[1],s.points[2],s.points[3],o,s.points[6]);break;case It.CURVE_TO:(o=a/s.pathLength)>1&&(o=1),i=this.getPointOnCubicBezier(o,s.start.x,s.start.y,s.points[0],s.points[1],s.points[2],s.points[3],s.points[4],s.points[5]);break;case It.QUAD_TO:(o=a/s.pathLength)>1&&(o=1),i=this.getPointOnQuadraticBezier(o,s.start.x,s.start.y,s.points[0],s.points[1],s.points[2],s.points[3])}if(i)return i;break}r+=s.pathLength}return null}getLineLength(t,e,r,i){return Math.sqrt((r-t)*(r-t)+(i-e)*(i-e))}getPathLength(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce(((t,e)=>e.pathLength>0?t+e.pathLength:t),0)),this.pathLength}getPointOnCubicBezier(t,e,r,i,n,s,a,o,h){return{x:o*it(t)+s*nt(t)+i*st(t)+e*at(t),y:h*it(t)+a*nt(t)+n*st(t)+r*at(t)}}getPointOnQuadraticBezier(t,e,r,i,n,s,a){return{x:s*ot(t)+i*ht(t)+e*lt(t),y:a*ot(t)+n*ht(t)+r*lt(t)}}getPointOnEllipticalArc(t,e,r,i,n,s){var a=Math.cos(s),o=Math.sin(s),h=r*Math.cos(n),l=i*Math.sin(n);return{x:t+(h*a-l*o),y:e+(h*o+l*a)}}buildEquidistantCache(t,e){var r=this.getPathLength(),i=e||.25,n=t||r/100;if(!this.equidistantCache||this.equidistantCache.step!==n||this.equidistantCache.precision!==i){this.equidistantCache={step:n,precision:i,points:[]};for(var s=0,a=0;a<=r;a+=i){var o=this.getPointOnPath(a),h=this.getPointOnPath(a+i);o&&h&&((s+=this.getLineLength(o.x,o.y,h.x,h.y))>=n&&(this.equidistantCache.points.push({x:o.x,y:o.y,distance:a}),s-=n))}}}getEquidistantPointOnPath(t,e,r){if(this.buildEquidistantCache(e,r),t<0||t-this.getPathLength()>5e-5)return null;var i=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[i]||null}}var pe=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class fe extends Lt{constructor(t,e,r){super(t,e,r),this.type="image",this.loaded=!1;var i=this.getHrefAttribute().getString();if(i){var n=i.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(i);t.images.push(this),n?this.loadSvg(i):this.loadImage(i),this.isSvg=n}}loadImage(t){var e=this;return n((function*(){try{var r=yield e.document.createImage(t);e.image=r}catch(i){console.error('Error while loading image "'.concat(t,'":'),i)}e.loaded=!0}))()}loadSvg(t){var e=this;return n((function*(){var r=pe.exec(t);if(r){var i=r[5];"base64"===r[4]?e.image=atob(i):e.image=decodeURIComponent(i)}else try{var n=yield e.document.fetch(t),s=yield n.text();e.image=s}catch(a){console.error('Error while loading image "'.concat(t,'":'),a)}e.loaded=!0}))()}renderChildren(t){var{document:e,image:r,loaded:i}=this,n=this.getAttribute("x").getPixels("x"),s=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y");if(i&&r&&a&&o){if(t.save(),t.translate(n,s),this.isSvg){var h=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:a,scaleHeight:o});h.document.documentElement.parent=this,h.render()}else{var l=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:a,desiredWidth:l.width,height:o,desiredHeight:l.height}),this.loaded&&("undefined"===typeof l.complete||l.complete)&&t.drawImage(l,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),i=this.getStyle("height").getPixels("y");return new Rt(t,e,t+r,e+i)}}class ye extends Lt{constructor(){super(...arguments),this.type="symbol"}render(t){}}class me{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var r=this;return n((function*(){try{var{document:i}=r,n=(yield i.canvg.parser.load(e)).getElementsByTagName("font");Array.from(n).forEach((e=>{var r=i.createElement(e);i.definitions[t]=r}))}catch(s){console.error('Error while loading font "'.concat(e,'":'),s)}r.loaded=!0}))()}}class ve extends Et{constructor(t,e,r){super(t,e,r),this.type="style";var i=L(Array.from(e.childNodes).map((t=>t.textContent)).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,""));i.split("}").forEach((e=>{var r=e.trim();if(r){var i=r.split("{"),n=i[0].split(","),s=i[1].split(";");n.forEach((e=>{var r=e.trim();if(r){var i=t.styles[r]||{};if(s.forEach((e=>{var r=e.indexOf(":"),n=e.substr(0,r).trim(),s=e.substr(r+1,e.length-r).trim();n&&s&&(i[n]=new ut(t,n,s))})),t.styles[r]=i,t.stylesSpecificity[r]=K(r),"@font-face"===r){var n=i["font-family"].getString().replace(/"|'/g,"");i.src.getString().split(",").forEach((e=>{if(e.indexOf('format("svg")')>0){var r=U(e);r&&new me(t).load(n,r)}}))}}}))}}))}}ve.parseExternalUrl=U;class xe extends Lt{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),r=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),r.hasValue()&&t.translate(0,r.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:r}=this;if(r){var i=r;if("symbol"===r.type&&((i=new Ut(e,null)).attributes.viewBox=new ut(e,"viewBox",r.getAttribute("viewBox").getString()),i.attributes.preserveAspectRatio=new ut(e,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),i.attributes.overflow=new ut(e,"overflow",r.getAttribute("overflow").getString()),i.children=r.children,r.styles.opacity=new ut(e,"opacity",this.calculateOpacity())),"svg"===i.type){var n=this.getStyle("width",!1,!0),s=this.getStyle("height",!1,!0);n.hasValue()&&(i.attributes.width=new ut(e,"width",n.getString())),s.hasValue()&&(i.attributes.height=new ut(e,"height",s.getString()))}var a=i.parent;i.parent=this,i.render(t),i.parent=a}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return Ot.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function be(t,e,r,i,n,s){return t[r*i*4+4*e+s]}function Se(t,e,r,i,n,s,a){t[r*i*4+4*e+s]=a}function we(t,e,r){return t[e]*r}function Te(t,e,r,i){return e+Math.cos(t)*r+Math.sin(t)*i}class Ae extends Et{constructor(t,e,r){super(t,e,r),this.type="feColorMatrix";var i=z(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var n=i[0];i=[.213+.787*n,.715-.715*n,.072-.072*n,0,0,.213-.213*n,.715+.285*n,.072-.072*n,0,0,.213-.213*n,.715-.715*n,.072+.928*n,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var s=i[0]*Math.PI/180;i=[Te(s,.213,.787,-.213),Te(s,.715,-.715,-.715),Te(s,.072,-.072,.928),0,0,Te(s,.213,-.213,.143),Te(s,.715,.285,.14),Te(s,.072,-.072,-.283),0,0,Te(s,.213,-.213,-.787),Te(s,.715,-.715,.715),Te(s,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=i,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,r,i,n){for(var{includeOpacity:s,matrix:a}=this,o=t.getImageData(0,0,i,n),h=0;h<n;h++)for(var l=0;l<i;l++){var u=be(o.data,l,h,i,0,0),c=be(o.data,l,h,i,0,1),g=be(o.data,l,h,i,0,2),d=be(o.data,l,h,i,0,3),p=we(a,0,u)+we(a,1,c)+we(a,2,g)+we(a,3,d)+we(a,4,1),f=we(a,5,u)+we(a,6,c)+we(a,7,g)+we(a,8,d)+we(a,9,1),y=we(a,10,u)+we(a,11,c)+we(a,12,g)+we(a,13,d)+we(a,14,1),m=we(a,15,u)+we(a,16,c)+we(a,17,g)+we(a,18,d)+we(a,19,1);s&&(p=0,f=0,y=0,m*=d/255),Se(o.data,l,h,i,0,0,p),Se(o.data,l,h,i,0,1,f),Se(o.data,l,h,i,0,2,y),Se(o.data,l,h,i,0,3,m)}t.clearRect(0,0,i,n),t.putImageData(o,0,0)}}class Ce extends Et{constructor(){super(...arguments),this.type="mask"}apply(t,e){var{document:r}=this,i=this.getAttribute("x").getPixels("x"),n=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),a=this.getStyle("height").getPixels("y");if(!s&&!a){var o=new Rt;this.children.forEach((e=>{o.addBoundingBox(e.getBoundingBox(t))})),i=Math.floor(o.x1),n=Math.floor(o.y1),s=Math.floor(o.width),a=Math.floor(o.height)}var h=this.removeStyles(e,Ce.ignoreStyles),l=r.createCanvas(i+s,n+a),u=l.getContext("2d");r.screen.setDefaults(u),this.renderChildren(u),new Ae(r,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(u,0,0,i+s,n+a);var c=r.createCanvas(i+s,n+a),g=c.getContext("2d");r.screen.setDefaults(g),e.render(g),g.globalCompositeOperation="destination-in",g.fillStyle=u.createPattern(l,"no-repeat"),g.fillRect(0,0,i+s,n+a),t.fillStyle=g.createPattern(c,"no-repeat"),t.fillRect(0,0,i+s,n+a),this.restoreStyles(e,h)}render(t){}}Ce.ignoreStyles=["mask","transform","clip-path"];var Pe=()=>{};class Oe extends Et{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,r=Reflect.getPrototypeOf(t),{beginPath:i,closePath:n}=t;r&&(r.beginPath=Pe,r.closePath=Pe),Reflect.apply(i,t,[]),this.children.forEach((i=>{if("undefined"!==typeof i.path){var s="undefined"!==typeof i.elementTransform?i.elementTransform():null;s||(s=Ot.fromElement(e,i)),s&&s.apply(t),i.path(t),r&&(r.closePath=n),s&&s.unapply(t)}})),Reflect.apply(n,t,[]),t.clip(),r&&(r.beginPath=i,r.closePath=n)}render(t){}}class Ee extends Et{constructor(){super(...arguments),this.type="filter"}apply(t,e){var{document:r,children:i}=this,n=e.getBoundingBox(t);if(n){var s=0,a=0;i.forEach((t=>{var e=t.extraFilterDistance||0;s=Math.max(s,e),a=Math.max(a,e)}));var o=Math.floor(n.width),h=Math.floor(n.height),l=o+2*s,u=h+2*a;if(!(l<1||u<1)){var c=Math.floor(n.x),g=Math.floor(n.y),d=this.removeStyles(e,Ee.ignoreStyles),p=r.createCanvas(l,u),f=p.getContext("2d");r.screen.setDefaults(f),f.translate(-c+s,-g+a),e.render(f),i.forEach((t=>{"function"===typeof t.apply&&t.apply(f,0,0,l,u)})),t.drawImage(p,0,0,l,u,c-s,g-a,l,u),this.restoreStyles(e,d)}}}render(t){}}Ee.ignoreStyles=["filter","transform","clip-path"];class Me extends Et{constructor(t,e,r){super(t,e,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,r,i,n){}}class Ne extends Et{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,r,i,n){}}class Ve extends Et{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,r,i,n){}}class _e extends Et{constructor(t,e,r){super(t,e,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,r,i,n){var{document:s,blurRadius:a}=this,o=s.window?s.window.document.body:null,h=t.canvas;h.id=s.getUniqueId(),o&&(h.style.display="none",o.appendChild(h)),k(h,e,r,i,n,a),o&&o.removeChild(h)}}class ke extends Et{constructor(){super(...arguments),this.type="title"}}class Re extends Et{constructor(){super(...arguments),this.type="desc"}}var Ie={svg:Ut,rect:Xt,circle:Yt,ellipse:jt,line:qt,polyline:Qt,polygon:Wt,path:Dt,pattern:Gt,marker:$t,defs:Zt,linearGradient:te,radialGradient:ee,stop:re,animate:ie,animateColor:ne,animateTransform:se,font:ae,"font-face":oe,"missing-glyph":he,glyph:Bt,text:zt,tspan:Ht,tref:le,a:ue,textPath:de,image:fe,g:Kt,symbol:ye,style:ve,use:xe,mask:Ce,clipPath:Oe,filter:Ee,feDropShadow:Me,feMorphology:Ne,feComposite:Ve,feColorMatrix:Ae,feGaussianBlur:_e,title:ke,desc:Re};function Le(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function De(){return De=n((function*(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.createElement("img");return e&&(r.crossOrigin="Anonymous"),new Promise(((e,i)=>{r.onload=()=>{e(r)},r.onerror=(t,e,r,n,s)=>{i(s)},r.src=t}))})),De.apply(this,arguments)}class Be{constructor(t){var{rootEmSize:e=12,emSize:r=12,createCanvas:i=Be.createCanvas,createImage:n=Be.createImage,anonymousCrossOrigin:s}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=t,this.definitions=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=t.screen,this.rootEmSize=e,this.emSize=r,this.createCanvas=i,this.createImage=this.bindCreateImage(n,s),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(t,e){return"boolean"===typeof e?(r,i)=>t(r,"boolean"===typeof i?i:e):t}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:t}=this;return t[t.length-1]}set emSize(t){var{emSizeStack:e}=this;e.push(t)}popEmSize(){var{emSizeStack:t}=this;t.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every((t=>t.loaded))}isFontsLoaded(){return this.fonts.every((t=>t.loaded))}createDocumentElement(t){var e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}createElement(t){var e=t.nodeName.replace(/^[^:]+:/,""),r=Be.elementTypes[e];return"undefined"!==typeof r?new r(this,t):new Mt(this,t)}createTextNode(t){return new Ft(this,t)}setViewBox(t){this.screen.setViewBox(function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Le(Object(r),!0).forEach((function(e){a(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Le(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({document:this},t))}}function ze(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function He(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ze(Object(r),!0).forEach((function(e){a(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ze(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}Be.createCanvas=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,r},Be.createImage=function(t){return De.apply(this,arguments)},Be.elementTypes=Ie;class Fe{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new xt(r),this.screen=new yt(t,r),this.options=r;var i=new Be(this,r),n=i.createDocumentElement(e);this.document=i,this.documentElement=n}static from(t,e){var r=arguments;return n((function*(){var i=r.length>2&&void 0!==r[2]?r[2]:{},n=new xt(i),s=yield n.parse(e);return new Fe(t,s,i)}))()}static fromString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=new xt(r).parseFromString(e);return new Fe(t,i,r)}fork(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Fe.from(t,e,He(He({},this.options),r))}forkString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Fe.fromString(t,e,He(He({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return n((function*(){var r=t.length>0&&void 0!==t[0]?t[0]:{};e.start(He({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},r)),yield e.ready(),e.stop()}))()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:r,options:i}=this;r.start(e,He(He({enableRedraw:!0},i),t))}stop(){this.screen.stop()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,r)}}}}]);
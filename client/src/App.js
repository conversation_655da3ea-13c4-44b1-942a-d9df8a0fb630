import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { GroupProvider } from './contexts/GroupContext';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import MultiTenantRegister from './components/auth/MultiTenantRegister';
import Dashboard from './components/dashboard/Dashboard';
import ProjectView from './components/project/ProjectView';
import RequirementView from './components/requirement/RequirementView';
import FeatureView from './components/feature/FeatureView';
import DocumentGenerator from './components/release/ReleaseNotesGenerator';
import ThemeDemo from './components/demo/ThemeDemo';
import LogoTestPage from './components/test/LogoTestPage';
import CollaborativeLayoutTest from './components/test/CollaborativeLayoutTest';
import CollaborativeLayoutTest2 from './components/test/CollaborativeLayoutTest2';
import CollaborativeLayoutTest3 from './components/test/CollaborativeLayoutTest3';
import ModularLayoutTest from './components/test/ModularLayoutTest';
import LayoutTest5 from './components/test/LayoutTest5';
import LayoutTest6 from './components/test/LayoutTest6';
import LayoutTest7 from './components/test/LayoutTest7';
import TimelineMockupDemo from './components/timeline/TimelineMockupDemo';
import MainLayout from './components/layout/MainLayout';
import socketManager from './utils/socket';
import { cleanProfessionalTheme } from './themes/cleanProfessional';

const PrivateRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  return isAuthenticated ? (
    <MainLayout>
      {children}
    </MainLayout>
  ) : (
    <Navigate to="/login" />
  );
};

const App = () => {
  useEffect(() => {
    // Initialize socket connection
    socketManager.initialize();
  }, []);

  return (
    <ThemeProvider theme={cleanProfessionalTheme}>
      <CssBaseline />
      <AuthProvider>
        <GroupProvider>
          <Router>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<MultiTenantRegister />} />
              <Route path="/register-legacy" element={<Register />} />
            <Route
              path="/"
              element={
                <PrivateRoute>
                  <Dashboard />
                </PrivateRoute>
              }
            />
            <Route
              path="/project/:projectId"
              element={
                <PrivateRoute>
                  <ProjectView />
                </PrivateRoute>
              }
            />
            <Route
              path="/requirement/:requirementId"
              element={
                <PrivateRoute>
                  <RequirementView />
                </PrivateRoute>
              }
            />
            <Route
              path="/feature/:featureId"
              element={
                <PrivateRoute>
                  <FeatureView />
                </PrivateRoute>
              }
            />
            <Route
              path="/project/:projectId/documents"
              element={
                <PrivateRoute>
                  <DocumentGenerator />
                </PrivateRoute>
              }
            />
            {/* Backward compatibility route */}
            <Route
              path="/project/:projectId/release-notes"
              element={
                <PrivateRoute>
                  <DocumentGenerator />
                </PrivateRoute>
              }
            />
            <Route path="/theme-demo" element={<ThemeDemo />} />
            <Route path="/logo-test" element={<LogoTestPage />} />
            <Route path="/layout-test" element={<CollaborativeLayoutTest />} />
            <Route path="/layout-test2" element={<CollaborativeLayoutTest2 />} />
            <Route path="/layout-test3" element={<CollaborativeLayoutTest3 />} />
            <Route path="/layout-test4" element={<ModularLayoutTest />} />
            <Route path="/layout-test5" element={<LayoutTest5 />} />
            <Route path="/layout-test6" element={<LayoutTest6 />} />
            <Route path="/layout-test7" element={<LayoutTest7 />} />
            <Route path="/timeline-demo" element={<TimelineMockupDemo />} />
            <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Router>
        </GroupProvider>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;
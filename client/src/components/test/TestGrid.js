import React, { useMemo, useRef } from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { Box } from '@mui/material';
import ResultEditor from './editors/ResultEditor';
import TextAreaEditor from './editors/TextAreaEditor';

const TestGrid = ({ testSuite, onTestDataChange, selectedRows, onSelectionChange }) => {
  const gridRef = useRef(null);
  const tests = testSuite?.tests || [];

  // Generate columns dynamically based on the number of result columns
  const columns = useMemo(() => {
    const baseColumns = [
      {
        field: 'testName',
        headerName: 'Test Name',
        width: 200,
        editable: true,
        renderEditCell: (params) => (
          <input
            type="text"
            value={params.value || ''}
            onChange={(e) => params.api.setEditCellValue({ id: params.id, field: params.field, value: e.target.value })}

            autoFocus
            style={{
              width: '100%',
              height: '100%',
              border: 'none',
              outline: 'none',
              padding: '8px',
              fontFamily: 'inherit',
              fontSize: 'inherit'
            }}
            placeholder="Enter test name..."
          />
        )
      },
      {
        field: 'setup',
        headerName: 'Setup',
        width: 200,
        editable: true,
        renderCell: (params) => (
          <Box sx={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word', padding: '8px 0' }}>
            {params.value}
          </Box>
        ),
        renderEditCell: (params) => (
          <textarea
            value={params.value || ''}
            onChange={(e) => params.api.setEditCellValue({ id: params.id, field: params.field, value: e.target.value })}
            onKeyDown={(e) => {
              // Allow Enter for newlines
              if (e.key === 'Enter') {
                e.stopPropagation(); // Prevent cell exit on Enter
              }
            }}
            autoFocus
            style={{
              width: '100%',
              height: '100%',
              border: 'none',
              outline: 'none',
              resize: 'none',
              padding: '8px',
              fontFamily: 'inherit',
              fontSize: 'inherit'
            }}
            placeholder="Enter setup instructions..."
          />
        )
      },
      {
        field: 'steps',
        headerName: 'Steps',
        width: 200,
        editable: true,
        renderCell: (params) => (
          <Box sx={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word', padding: '8px 0' }}>
            {params.value}
          </Box>
        ),
        renderEditCell: (params) => (
          <textarea
            value={params.value || ''}
            onChange={(e) => params.api.setEditCellValue({ id: params.id, field: params.field, value: e.target.value })}
            onKeyDown={(e) => {
              // Allow Enter for newlines
              if (e.key === 'Enter') {
                e.stopPropagation(); // Prevent cell exit on Enter
              }
            }}
            autoFocus
            style={{
              width: '100%',
              height: '100%',
              border: 'none',
              outline: 'none',
              resize: 'none',
              padding: '8px',
              fontFamily: 'inherit',
              fontSize: 'inherit'
            }}
            placeholder="Enter test steps (one per line)..."
          />
        )
      },
      {
        field: 'expectedResult',
        headerName: 'Expected Result',
        width: 200,
        editable: true,
        renderCell: (params) => (
          <Box sx={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word', padding: '8px 0' }}>
            {params.value}
          </Box>
        ),
        renderEditCell: (params) => (
          <textarea
            value={params.value || ''}
            onChange={(e) => params.api.setEditCellValue({ id: params.id, field: params.field, value: e.target.value })}
            onKeyDown={(e) => {
              // Allow Enter for newlines
              if (e.key === 'Enter') {
                e.stopPropagation(); // Prevent cell exit on Enter
              }
            }}
            autoFocus
            style={{
              width: '100%',
              height: '100%',
              border: 'none',
              outline: 'none',
              resize: 'none',
              padding: '8px',
              fontFamily: 'inherit',
              fontSize: 'inherit'
            }}
            placeholder="Enter expected result..."
          />
        )
      }
    ];

    // Add result columns dynamically
    const resultColumns = [];
    if (tests.length > 0) {
      const maxResults = Math.max(...tests.map(test => test.results?.length || 0));

      for (let i = 0; i < Math.max(2, maxResults); i++) {
        resultColumns.push({
          field: `result${i}`,
          headerName: `Result ${i + 1}`,
          width: 150,
          editable: true,
          renderCell: (params) => {
            const result = params.value;
            if (!result || !result.status) {
              return (
                <div
                  style={{
                    color: '#999',
                    cursor: 'pointer',
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                  onClick={() => params.api.startCellEditMode({ id: params.id, field: params.field })}
                >
                  Click to set
                </div>
              );
            }

            const statusStyle = {
              padding: '4px 8px',
              borderRadius: '4px',
              color: 'white',
              fontSize: '12px',
              fontWeight: 'bold',
              cursor: 'pointer'
            };

            switch (result.status) {
              case 'Pass':
                statusStyle.backgroundColor = '#22c55e';
                break;
              case 'Fail':
                statusStyle.backgroundColor = '#ef4444';
                break;
              case 'Skip':
                statusStyle.backgroundColor = '#eab308';
                statusStyle.color = 'black';
                break;
              default:
                return '';
            }

            return (
              <Box
                sx={statusStyle}
                onClick={() => params.api.startCellEditMode({ id: params.id, field: params.field })}
              >
                {result.status}
                {result.date && (
                  <Box sx={{ fontSize: '10px', marginTop: '2px' }}>
                    {new Date(result.date).toLocaleDateString()}
                  </Box>
                )}
              </Box>
            );
          },
          renderEditCell: (params) => {
            const currentValue = params.value || { status: null, date: null };

            return (
              <Box sx={{ padding: '4px', width: '100%', display: 'flex', flexDirection: 'column', gap: '4px' }}>
                <select
                  value={currentValue.status || ''}
                  onChange={(e) => {
                    const newStatus = e.target.value || null;
                    const newDate = newStatus ? (currentValue.date || new Date().toISOString().split('T')[0]) : null;
                    const newValue = { status: newStatus, date: newDate };
                    params.api.setEditCellValue({ id: params.id, field: params.field, value: newValue });
                  }}

                  autoFocus
                  style={{
                    width: '100%',
                    padding: '4px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '12px'
                  }}
                >
                  <option value="">Select...</option>
                  <option value="Pass">Pass</option>
                  <option value="Fail">Fail</option>
                  <option value="Skip">Skip</option>
                </select>
                {currentValue.status && (
                  <input
                    type="date"
                    value={currentValue.date ? currentValue.date.split('T')[0] : new Date().toISOString().split('T')[0]}
                    onChange={(e) => {
                      const newValue = {
                        status: currentValue.status,
                        date: e.target.value ? new Date(e.target.value).toISOString() : null
                      };
                      params.api.setEditCellValue({ id: params.id, field: params.field, value: newValue });
                    }}

                    style={{
                      width: '100%',
                      padding: '2px',
                      border: '1px solid #ddd',
                      borderRadius: '4px',
                      fontSize: '10px'
                    }}
                  />
                )}
              </Box>
            );
          }
        });
      }
    } else {
      // Default 2 result columns
      for (let i = 0; i < 2; i++) {
        resultColumns.push({
          field: `result${i}`,
          headerName: `Result ${i + 1}`,
          width: 150,
          editable: true,
          renderCell: (params) => {
            const result = params.value || { status: null, date: null };
            if (!result.status) {
              return (
                <div
                  style={{
                    color: '#999',
                    cursor: 'pointer',
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                  onClick={() => params.api.startCellEditMode({ id: params.id, field: params.field })}
                >
                  Click to set
                </div>
              );
            }

            const cellClass = `result-${result.status.toLowerCase()}`;
            const displayDate = result.date ? new Date(result.date).toLocaleDateString() : '';

            return (
              <div
                className={cellClass}
                style={{
                  padding: '4px 8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  textAlign: 'center',
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  cursor: 'pointer'
                }}
                onClick={() => params.api.startCellEditMode({ id: params.id, field: params.field })}
              >
                <div>{result.status}</div>
                {displayDate && <div style={{ fontSize: '10px', opacity: 0.8 }}>{displayDate}</div>}
              </div>
            );
          },
          renderEditCell: (params) => {
            const currentValue = params.value || { status: null, date: null };

            return (
              <Box sx={{ padding: '4px', width: '100%', display: 'flex', flexDirection: 'column', gap: '4px' }}>
                <select
                  value={currentValue.status || ''}
                  onChange={(e) => {
                    const newStatus = e.target.value || null;
                    const newDate = newStatus ? (currentValue.date || new Date().toISOString().split('T')[0]) : null;
                    const newValue = { status: newStatus, date: newDate };
                    params.api.setEditCellValue({ id: params.id, field: params.field, value: newValue });
                  }}

                  autoFocus
                  style={{
                    width: '100%',
                    padding: '4px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '12px'
                  }}
                >
                  <option value="">Select...</option>
                  <option value="Pass">Pass</option>
                  <option value="Fail">Fail</option>
                  <option value="Skip">Skip</option>
                </select>
                {currentValue.status && (
                  <input
                    type="date"
                    value={currentValue.date ? currentValue.date.split('T')[0] : new Date().toISOString().split('T')[0]}
                    onChange={(e) => {
                      const newValue = {
                        status: currentValue.status,
                        date: e.target.value ? new Date(e.target.value).toISOString() : null
                      };
                      params.api.setEditCellValue({ id: params.id, field: params.field, value: newValue });
                    }}

                    style={{
                      width: '100%',
                      padding: '2px',
                      border: '1px solid #ddd',
                      borderRadius: '4px',
                      fontSize: '10px'
                    }}
                  />
                )}
              </Box>
            );
          }
        });
      }
    }

    return [...baseColumns, ...resultColumns];
  }, [tests]);

  // Transform tests data for the grid
  const rows = useMemo(() => {
    return tests.map((test, index) => {
      const row = {
        id: test.id || `test_${index}`,
        testName: test.testName || '',
        setup: test.setup || '',
        steps: test.steps || '',
        expectedResult: test.expectedResult || ''
      };

      // Add result columns
      if (test.results) {
        test.results.forEach((result, i) => {
          row[`result${i}`] = result || { status: null, date: null };
        });
      }

      return row;
    });
  }, [tests]);

  const processRowUpdate = (newRow, oldRow) => {
    // Find the test and update it
    const updatedTests = tests.map(test => {
      if (test.id === newRow.id || `test_${tests.indexOf(test)}` === newRow.id) {
        const updatedTest = { ...test };

        // Update all fields from the new row
        Object.keys(newRow).forEach(field => {
          if (field === 'id') return; // Skip id field

          if (field.startsWith('result')) {
            const resultIndex = parseInt(field.replace('result', ''));
            if (!updatedTest.results) updatedTest.results = [];

            // Ensure results array is long enough
            while (updatedTest.results.length <= resultIndex) {
              updatedTest.results.push(null);
            }

            updatedTest.results[resultIndex] = newRow[field];
          } else {
            updatedTest[field] = newRow[field];
          }
        });

        return updatedTest;
      }
      return test;
    });

    onTestDataChange(updatedTests);
    return newRow;
  };

  const handleProcessRowUpdateError = (error) => {
    console.error('Error updating row:', error);
  };

  return (
    <Box
      className="test-grid-container"
      sx={{ height: 400, width: '100%' }}
      onKeyDown={(event) => {
        // Trap tab navigation within the grid
        if (event.key === 'Tab') {
          event.preventDefault();
          event.stopPropagation();
        }
      }}
    >
      <DataGrid
        ref={gridRef}
        columns={columns}
        rows={rows}
        processRowUpdate={processRowUpdate}
        onProcessRowUpdateError={handleProcessRowUpdateError}
        className="test-grid"
        rowHeight={80}
        checkboxSelection
        disableSelectionOnClick={true}
        experimentalFeatures={{ newEditingApi: true }}
        initialState={{
          pagination: {
            paginationModel: { pageSize: 20 }
          }
        }}
        pageSizeOptions={[20, 50, 100]}
        rowSelectionModel={selectedRows}
        onRowSelectionModelChange={onSelectionChange}
        onKeyDown={(event) => {
          // Handle Tab key for navigation within the grid
          if (event.key === 'Tab') {
            event.preventDefault();
            event.stopPropagation();

            // Get the currently focused cell
            const activeElement = document.activeElement;
            const gridContainer = gridRef.current;

            if (gridContainer && activeElement) {
              // Find all focusable cells in the grid
              const cells = gridContainer.querySelectorAll('[role="gridcell"][tabindex="0"], [role="gridcell"] input, [role="gridcell"] textarea, [role="gridcell"] select');
              const cellsArray = Array.from(cells);
              const currentIndex = cellsArray.findIndex(cell =>
                cell === activeElement || cell.contains(activeElement)
              );

              if (currentIndex !== -1) {
                let nextIndex;
                if (event.shiftKey) {
                  // Shift+Tab: Move backward
                  nextIndex = currentIndex - 1;
                  if (nextIndex < 0) nextIndex = cellsArray.length - 1;
                } else {
                  // Tab: Move forward
                  nextIndex = currentIndex + 1;
                  if (nextIndex >= cellsArray.length) nextIndex = 0;
                }

                const nextCell = cellsArray[nextIndex];
                if (nextCell) {
                  nextCell.focus();
                  if (nextCell.click) nextCell.click();
                }
              }
            }
          }
        }}
      />
    </Box>
  );
};

export default TestGrid;

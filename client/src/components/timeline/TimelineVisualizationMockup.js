import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Avatar,
  IconButton,
  Divider,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Paper
} from '@mui/material';
import {
  Edit as EditIcon,
  Comment as CommentIcon,
  ChangeCircle as StateIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  Timeline as TimelineIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon
} from '@mui/icons-material';

const TimelineVisualizationMockup = () => {
  const [viewMode, setViewMode] = useState('detailed');
  const [filterBy, setFilterBy] = useState('all');
  const [showVersions, setShowVersions] = useState(true);

  // Mock timeline data
  const timelineEvents = [
    {
      id: 1,
      type: 'created',
      user: { name: '<PERSON>', avatar: 'J<PERSON>' },
      timestamp: '2024-01-15T10:30:00Z',
      description: 'Requirement created',
      version: 1,
      data: { title: 'User Authentication System' }
    },
    {
      id: 2,
      type: 'state_changed',
      user: { name: 'Jane <PERSON>', avatar: 'JS' },
      timestamp: '2024-01-15T14:20:00Z',
      description: 'State changed from New to Being Drafted',
      version: 1,
      data: { from: 'New', to: 'Being Drafted' }
    },
    {
      id: 3,
      type: 'version_created',
      user: { name: 'John Doe', avatar: 'JD' },
      timestamp: '2024-01-16T09:15:00Z',
      description: 'New version created with title changes',
      version: 2,
      data: { changes: ['title', 'description'] }
    },
    {
      id: 4,
      type: 'comment_added',
      user: { name: 'Mike Johnson', avatar: 'MJ' },
      timestamp: '2024-01-16T11:45:00Z',
      description: 'Added comment about security requirements',
      version: 2,
      data: { comment: 'We need to ensure 2FA is included...' }
    },
    {
      id: 5,
      type: 'state_changed',
      user: { name: 'Jane Smith', avatar: 'JS' },
      timestamp: '2024-01-17T08:30:00Z',
      description: 'State changed to Requiring Approval',
      version: 2,
      data: { from: 'Being Drafted', to: 'Requiring Approval' }
    }
  ];

  const getEventIcon = (type) => {
    switch (type) {
      case 'created': return <AddIcon />;
      case 'state_changed': return <StateIcon />;
      case 'version_created': return <EditIcon />;
      case 'comment_added': return <CommentIcon />;
      default: return <TimelineIcon />;
    }
  };

  const getEventColor = (type) => {
    switch (type) {
      case 'created': return 'success';
      case 'state_changed': return 'warning';
      case 'version_created': return 'primary';
      case 'comment_added': return 'info';
      default: return 'grey';
    }
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          📊 Timeline Visualization Component
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Interactive timeline showing requirement history with filtering and different view modes
        </Typography>
      </Box>

      {/* Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>View Mode</InputLabel>
              <Select
                value={viewMode}
                label="View Mode"
                onChange={(e) => setViewMode(e.target.value)}
              >
                <MenuItem value="compact">Compact</MenuItem>
                <MenuItem value="detailed">Detailed</MenuItem>
                <MenuItem value="grouped">Grouped by Day</MenuItem>
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Filter</InputLabel>
              <Select
                value={filterBy}
                label="Filter"
                onChange={(e) => setFilterBy(e.target.value)}
              >
                <MenuItem value="all">All Events</MenuItem>
                <MenuItem value="state_changed">State Changes</MenuItem>
                <MenuItem value="version_created">Versions</MenuItem>
                <MenuItem value="comment_added">Comments</MenuItem>
              </Select>
            </FormControl>

            <FormControlLabel
              control={
                <Switch
                  checked={showVersions}
                  onChange={(e) => setShowVersions(e.target.checked)}
                />
              }
              label="Show Versions"
            />

            <Box sx={{ ml: 'auto', display: 'flex', gap: 1 }}>
              <Button startIcon={<FilterIcon />} variant="outlined" size="small">
                Advanced Filters
              </Button>
              <Button startIcon={<DownloadIcon />} variant="outlined" size="small">
                Export
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Timeline Visualization */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <TimelineIcon sx={{ mr: 1 }} />
            <Typography variant="h6">
              Requirement Timeline - TEST-R-001
            </Typography>
            <Chip 
              label={`${timelineEvents.length} events`} 
              size="small" 
              sx={{ ml: 2 }} 
            />
          </Box>

          <Box sx={{ position: 'relative' }}>
            {timelineEvents.map((event, index) => (
              <Box key={event.id} sx={{ display: 'flex', mb: 3 }}>
                {/* Timeline Line and Dot */}
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mr: 2 }}>
                  <Avatar
                    sx={{
                      bgcolor: `${getEventColor(event.type)}.main`,
                      width: 40,
                      height: 40
                    }}
                  >
                    {getEventIcon(event.type)}
                  </Avatar>
                  {index < timelineEvents.length - 1 && (
                    <Box
                      sx={{
                        width: 2,
                        height: 60,
                        bgcolor: 'grey.300',
                        mt: 1
                      }}
                    />
                  )}
                </Box>

                {/* Timeline Content */}
                <Box sx={{ flexGrow: 1 }}>
                  <Card 
                    variant="outlined" 
                    sx={{ 
                      mb: 2,
                      '&:hover': { 
                        boxShadow: 2,
                        transform: 'translateY(-1px)',
                        transition: 'all 0.2s ease-in-out'
                      }
                    }}
                  >
                    <CardContent sx={{ pb: '16px !important' }}>
                      {/* Event Header */}
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Avatar sx={{ width: 32, height: 32, mr: 2, fontSize: '0.875rem' }}>
                          {event.user.avatar}
                        </Avatar>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {event.user.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {formatTimestamp(event.timestamp)}
                          </Typography>
                        </Box>
                        {showVersions && (
                          <Chip 
                            label={`v${event.version}`} 
                            size="small" 
                            variant="outlined"
                            color="primary"
                          />
                        )}
                      </Box>

                      {/* Event Description */}
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        {event.description}
                      </Typography>

                      {/* Event Details */}
                      {viewMode === 'detailed' && (
                        <Box sx={{ mt: 1 }}>
                          {event.type === 'state_changed' && (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip label={event.data.from} size="small" variant="outlined" />
                              <Typography variant="caption">→</Typography>
                              <Chip label={event.data.to} size="small" color="primary" />
                            </Box>
                          )}
                          
                          {event.type === 'version_created' && (
                            <Box>
                              <Typography variant="caption" color="text.secondary">
                                Changes: {event.data.changes.join(', ')}
                              </Typography>
                            </Box>
                          )}
                          
                          {event.type === 'comment_added' && (
                            <Box sx={{ 
                              bgcolor: 'grey.50', 
                              p: 1, 
                              borderRadius: 1,
                              border: '1px solid',
                              borderColor: 'grey.200'
                            }}>
                              <Typography variant="caption" sx={{ fontStyle: 'italic' }}>
                                "{event.data.comment}"
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      )}

                      {/* Action Buttons */}
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                        <IconButton size="small" sx={{ opacity: 0.7 }}>
                          <ViewIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              </Box>
            ))}
          </Box>

          {/* Timeline Stats */}
          <Divider sx={{ my: 2 }} />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              Timeline spans 3 days • Last updated 2 hours ago
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Chip label="2 versions" size="small" variant="outlined" />
              <Chip label="3 state changes" size="small" variant="outlined" />
              <Chip label="1 comment" size="small" variant="outlined" />
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default TimelineVisualizationMockup;

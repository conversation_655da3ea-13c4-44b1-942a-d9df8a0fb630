import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Avatar,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Button,
  Badge,
  Tabs,
  Tab,
  TextField,
  InputAdornment,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Edit as EditIcon,
  Comment as CommentIcon,
  ChangeCircle as StateIcon,
  Add as AddIcon,
  Notifications as NotificationIcon,
  Search as SearchIcon,
  MoreVert as MoreIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';

const ActivityFeedMockup = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);

  // Mock activity data
  const activities = [
    {
      id: 1,
      type: 'requirement_created',
      user: { name: '<PERSON>', avatar: 'JD' },
      timestamp: '5 minutes ago',
      title: 'Created new requirement',
      description: 'User Authentication System (TEST-R-001)',
      project: 'Authentication Project',
      priority: 'high',
      unread: true
    },
    {
      id: 2,
      type: 'state_changed',
      user: { name: 'Jane Smith', avatar: 'JS' },
      timestamp: '12 minutes ago',
      title: 'Changed requirement state',
      description: 'TEST-R-002: Payment Gateway → Being Drafted → Requiring Approval',
      project: 'E-commerce Platform',
      priority: 'medium',
      unread: true
    },
    {
      id: 3,
      type: 'comment_added',
      user: { name: 'Mike Johnson', avatar: 'MJ' },
      timestamp: '1 hour ago',
      title: 'Added comment',
      description: 'TEST-R-001: "We should consider OAuth 2.0 implementation..."',
      project: 'Authentication Project',
      priority: 'low',
      unread: false
    },
    {
      id: 4,
      type: 'feature_created',
      user: { name: 'Sarah Wilson', avatar: 'SW' },
      timestamp: '2 hours ago',
      title: 'Created new feature',
      description: 'Social Media Integration (TEST-F-003)',
      project: 'Marketing Platform',
      priority: 'medium',
      unread: false
    },
    {
      id: 5,
      type: 'version_created',
      user: { name: 'John Doe', avatar: 'JD' },
      timestamp: '3 hours ago',
      title: 'Created new version',
      description: 'TEST-R-001: Updated description and acceptance criteria',
      project: 'Authentication Project',
      priority: 'low',
      unread: false
    },
    {
      id: 6,
      type: 'approval_requested',
      user: { name: 'Jane Smith', avatar: 'JS' },
      timestamp: '4 hours ago',
      title: 'Requested approval',
      description: 'TEST-R-004: Database Schema Design needs review',
      project: 'Backend Infrastructure',
      priority: 'high',
      unread: false
    }
  ];

  const getActivityIcon = (type) => {
    switch (type) {
      case 'requirement_created':
      case 'feature_created': return <AddIcon />;
      case 'state_changed': return <StateIcon />;
      case 'comment_added': return <CommentIcon />;
      case 'version_created': return <EditIcon />;
      case 'approval_requested': return <NotificationIcon />;
      default: return <ScheduleIcon />;
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'requirement_created':
      case 'feature_created': return 'success';
      case 'state_changed': return 'warning';
      case 'comment_added': return 'info';
      case 'version_created': return 'primary';
      case 'approval_requested': return 'error';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const tabLabels = ['All Activity', 'My Items', 'Mentions', 'Approvals'];
  const unreadCount = activities.filter(a => a.unread).length;

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          📱 Activity Feed Component
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Real-time activity stream with notifications, filtering, and social features
        </Typography>
      </Box>

      {/* Activity Feed Card */}
      <Card>
        {/* Header with Tabs and Controls */}
        <CardContent sx={{ pb: 0 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
              <NotificationIcon sx={{ mr: 1 }} />
              Activity Feed
              {unreadCount > 0 && (
                <Badge badgeContent={unreadCount} color="error" sx={{ ml: 1 }}>
                  <Box />
                </Badge>
              )}
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton size="small">
                <RefreshIcon />
              </IconButton>
              <IconButton size="small">
                <FilterIcon />
              </IconButton>
              <IconButton 
                size="small"
                onClick={(e) => setAnchorEl(e.currentTarget)}
              >
                <MoreIcon />
              </IconButton>
            </Box>
          </Box>

          {/* Tabs */}
          <Tabs 
            value={activeTab} 
            onChange={(e, newValue) => setActiveTab(newValue)}
            variant="scrollable"
            scrollButtons="auto"
          >
            {tabLabels.map((label, index) => (
              <Tab 
                key={index}
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {label}
                    {index === 0 && unreadCount > 0 && (
                      <Badge badgeContent={unreadCount} color="error" sx={{ ml: 1 }}>
                        <Box />
                      </Badge>
                    )}
                  </Box>
                }
              />
            ))}
          </Tabs>

          {/* Search */}
          <TextField
            fullWidth
            size="small"
            placeholder="Search activities..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ mt: 2 }}
          />
        </CardContent>

        {/* Activity List */}
        <List sx={{ pt: 0 }}>
          {activities.map((activity, index) => (
            <React.Fragment key={activity.id}>
              <ListItem
                sx={{
                  bgcolor: activity.unread ? 'action.hover' : 'transparent',
                  '&:hover': { bgcolor: 'action.selected' },
                  borderLeft: activity.unread ? '4px solid' : '4px solid transparent',
                  borderLeftColor: activity.unread ? 'primary.main' : 'transparent',
                  cursor: 'pointer'
                }}
              >
                <ListItemAvatar>
                  <Badge
                    overlap="circular"
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                    badgeContent={
                      <Avatar 
                        sx={{ 
                          width: 20, 
                          height: 20, 
                          bgcolor: `${getActivityColor(activity.type)}.main`,
                          border: '2px solid white'
                        }}
                      >
                        {getActivityIcon(activity.type)}
                      </Avatar>
                    }
                  >
                    <Avatar sx={{ bgcolor: 'grey.300' }}>
                      {activity.user.avatar}
                    </Avatar>
                  </Badge>
                </ListItemAvatar>

                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: activity.unread ? 600 : 400 }}>
                        {activity.user.name} {activity.title.toLowerCase()}
                      </Typography>
                      <Chip 
                        label={activity.priority} 
                        size="small" 
                        color={getPriorityColor(activity.priority)}
                        variant="outlined"
                        sx={{ height: 20, fontSize: '0.7rem' }}
                      />
                      {activity.unread && (
                        <Chip 
                          label="NEW" 
                          size="small" 
                          color="primary"
                          sx={{ height: 20, fontSize: '0.7rem' }}
                        />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                        {activity.description}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip 
                          label={activity.project} 
                          size="small" 
                          variant="outlined"
                          sx={{ height: 18, fontSize: '0.65rem' }}
                        />
                        <Typography variant="caption" color="text.secondary">
                          {activity.timestamp}
                        </Typography>
                      </Box>
                    </Box>
                  }
                />

                <ListItemSecondaryAction>
                  <IconButton size="small" sx={{ opacity: 0.7 }}>
                    <StarIcon fontSize="small" />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
              
              {index < activities.length - 1 && <Divider variant="inset" component="li" />}
            </React.Fragment>
          ))}
        </List>

        {/* Footer */}
        <CardContent sx={{ pt: 1 }}>
          <Divider sx={{ mb: 2 }} />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              {activities.length} activities • Last updated just now
            </Typography>
            <Button size="small" variant="outlined">
              Load More
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
      >
        <MenuItem onClick={() => setAnchorEl(null)}>Mark all as read</MenuItem>
        <MenuItem onClick={() => setAnchorEl(null)}>Export activities</MenuItem>
        <MenuItem onClick={() => setAnchorEl(null)}>Notification settings</MenuItem>
      </Menu>
    </Box>
  );
};

export default ActivityFeedMockup;

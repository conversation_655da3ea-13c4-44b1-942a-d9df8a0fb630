import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  LinearProgress,
  Avatar,
  AvatarGroup,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Timeline as TimelineIcon,
  Speed as SpeedIcon,
  People as PeopleIcon,
  Assignment as AssignmentIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon
} from '@mui/icons-material';

const TimelineStatsDashboard = () => {
  const [timeRange, setTimeRange] = useState('7d');

  // Mock statistics data
  const stats = {
    overview: {
      totalEvents: 1247,
      activeRequirements: 23,
      avgResponseTime: '2.3h',
      completionRate: 87
    },
    trends: {
      eventsThisWeek: { value: 156, change: +12 },
      stateChanges: { value: 34, change: -5 },
      newRequirements: { value: 8, change: +3 },
      comments: { value: 67, change: +18 }
    },
    topUsers: [
      { name: 'John Doe', avatar: 'JD', events: 45, trend: 'up' },
      { name: 'Jane Smith', avatar: 'JS', events: 38, trend: 'up' },
      { name: 'Mike Johnson', avatar: 'MJ', events: 29, trend: 'down' },
      { name: 'Sarah Wilson', avatar: 'SW', events: 24, trend: 'up' }
    ],
    projectActivity: [
      { name: 'Authentication System', events: 89, progress: 75, status: 'active' },
      { name: 'Payment Gateway', events: 67, progress: 45, status: 'active' },
      { name: 'User Dashboard', events: 34, progress: 90, status: 'review' },
      { name: 'Mobile App', events: 23, progress: 20, status: 'planning' }
    ],
    recentMilestones: [
      { title: 'Authentication MVP Complete', date: '2 days ago', type: 'success' },
      { title: 'Payment Integration Started', date: '5 days ago', type: 'info' },
      { title: 'Security Review Pending', date: '1 week ago', type: 'warning' }
    ]
  };

  const StatCard = ({ title, value, subtitle, icon, trend, color = 'primary' }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography color="text.secondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" color={`${color}.main`}>
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            {icon}
            {trend && (
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {trend > 0 ? (
                  <TrendingUpIcon color="success" fontSize="small" />
                ) : (
                  <TrendingDownIcon color="error" fontSize="small" />
                )}
                <Typography 
                  variant="caption" 
                  color={trend > 0 ? 'success.main' : 'error.main'}
                  sx={{ ml: 0.5 }}
                >
                  {trend > 0 ? '+' : ''}{trend}%
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ p: 3, maxWidth: 1400, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          📈 Timeline Statistics Dashboard
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Analytics and insights for timeline activity across all projects
        </Typography>
      </Box>

      {/* Controls */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Time Range</InputLabel>
          <Select
            value={timeRange}
            label="Time Range"
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <MenuItem value="24h">Last 24 Hours</MenuItem>
            <MenuItem value="7d">Last 7 Days</MenuItem>
            <MenuItem value="30d">Last 30 Days</MenuItem>
            <MenuItem value="90d">Last 90 Days</MenuItem>
          </Select>
        </FormControl>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton>
            <RefreshIcon />
          </IconButton>
          <IconButton>
            <DownloadIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Overview Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Events"
            value={stats.overview.totalEvents.toLocaleString()}
            subtitle="All timeline events"
            icon={<TimelineIcon color="primary" />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Requirements"
            value={stats.overview.activeRequirements}
            subtitle="In progress"
            icon={<AssignmentIcon color="success" />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Avg Response Time"
            value={stats.overview.avgResponseTime}
            subtitle="For state changes"
            icon={<SpeedIcon color="warning" />}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Completion Rate"
            value={`${stats.overview.completionRate}%`}
            subtitle="Requirements completed"
            icon={<CheckCircleIcon color="success" />}
            color="success"
          />
        </Grid>
      </Grid>

      {/* Trends and Activity */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Activity Trends */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Activity Trends
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h5" color="primary">
                      {stats.trends.eventsThisWeek.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Events This Week
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                      <TrendingUpIcon color="success" fontSize="small" />
                      <Typography variant="caption" color="success.main" sx={{ ml: 0.5 }}>
                        +{stats.trends.eventsThisWeek.change}%
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h5" color="warning.main">
                      {stats.trends.stateChanges.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      State Changes
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                      <TrendingDownIcon color="error" fontSize="small" />
                      <Typography variant="caption" color="error.main" sx={{ ml: 0.5 }}>
                        {stats.trends.stateChanges.change}%
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h5" color="success.main">
                      {stats.trends.newRequirements.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      New Requirements
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                      <TrendingUpIcon color="success" fontSize="small" />
                      <Typography variant="caption" color="success.main" sx={{ ml: 0.5 }}>
                        +{stats.trends.newRequirements.change}%
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h5" color="info.main">
                      {stats.trends.comments.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Comments Added
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                      <TrendingUpIcon color="success" fontSize="small" />
                      <Typography variant="caption" color="success.main" sx={{ ml: 0.5 }}>
                        +{stats.trends.comments.change}%
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Contributors */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PeopleIcon sx={{ mr: 1 }} />
                <Typography variant="h6">
                  Top Contributors
                </Typography>
              </Box>
              <List dense>
                {stats.topUsers.map((user, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <Avatar sx={{ width: 32, height: 32 }}>
                        {user.avatar}
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={user.name}
                      secondary={`${user.events} events`}
                    />
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {user.trend === 'up' ? (
                        <TrendingUpIcon color="success" fontSize="small" />
                      ) : (
                        <TrendingDownIcon color="error" fontSize="small" />
                      )}
                      <Chip 
                        label={`#${index + 1}`} 
                        size="small" 
                        color={index === 0 ? 'primary' : 'default'}
                        sx={{ ml: 1 }}
                      />
                    </Box>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Project Activity and Milestones */}
      <Grid container spacing={3}>
        {/* Project Activity */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Project Activity
              </Typography>
              {stats.projectActivity.map((project, index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2">
                      {project.name}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Chip 
                        label={project.status} 
                        size="small" 
                        color={
                          project.status === 'active' ? 'success' :
                          project.status === 'review' ? 'warning' : 'default'
                        }
                      />
                      <Typography variant="caption" color="text.secondary">
                        {project.events} events
                      </Typography>
                    </Box>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={project.progress} 
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                  <Typography variant="caption" color="text.secondary">
                    {project.progress}% complete
                  </Typography>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Milestones */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Milestones
              </Typography>
              <List dense>
                {stats.recentMilestones.map((milestone, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      {milestone.type === 'success' && <CheckCircleIcon color="success" />}
                      {milestone.type === 'warning' && <WarningIcon color="warning" />}
                      {milestone.type === 'info' && <InfoIcon color="info" />}
                    </ListItemIcon>
                    <ListItemText
                      primary={milestone.title}
                      secondary={milestone.date}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TimelineStatsDashboard;

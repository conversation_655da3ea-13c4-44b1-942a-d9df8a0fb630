import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Alert,
  AlertTitle,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider
} from '@mui/material';
import {
  Storage as StorageIcon,
  Warning as WarningIcon,
  Delete as DeleteIcon,
  Archive as ArchiveIcon,
  Schedule as ScheduleIcon,
  Settings as SettingsIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  Security as SecurityIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';

const AdminRetentionMockup = () => {
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState('');
  const [autoArchive, setAutoArchive] = useState(true);

  // Mock retention data
  const retentionStats = {
    totalEvents: 15847,
    archivedEvents: 3421,
    storageUsed: '2.3 GB',
    storageLimit: '10 GB',
    oldestEvent: '2023-01-15',
    retentionPeriod: '2 years'
  };

  const retentionPolicies = [
    {
      id: 1,
      name: 'Standard Retention',
      description: 'Archive events older than 1 year, delete after 3 years',
      archiveAfter: '1 year',
      deleteAfter: '3 years',
      status: 'active',
      affectedEvents: 1247
    },
    {
      id: 2,
      name: 'Compliance Retention',
      description: 'Archive after 6 months, delete after 7 years',
      archiveAfter: '6 months',
      deleteAfter: '7 years',
      status: 'inactive',
      affectedEvents: 0
    },
    {
      id: 3,
      name: 'Short-term Retention',
      description: 'Archive after 3 months, delete after 1 year',
      archiveAfter: '3 months',
      deleteAfter: '1 year',
      status: 'inactive',
      affectedEvents: 0
    }
  ];

  const upcomingActions = [
    {
      action: 'Archive',
      count: 156,
      date: '2024-01-20',
      type: 'automatic',
      severity: 'info'
    },
    {
      action: 'Delete',
      count: 23,
      date: '2024-01-25',
      type: 'automatic',
      severity: 'warning'
    },
    {
      action: 'Review Required',
      count: 8,
      date: '2024-01-22',
      type: 'manual',
      severity: 'error'
    }
  ];

  const storageBreakdown = [
    { category: 'Active Events', size: '1.8 GB', percentage: 78 },
    { category: 'Archived Events', size: '0.4 GB', percentage: 17 },
    { category: 'Metadata', size: '0.1 GB', percentage: 5 }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'default';
      case 'warning': return 'warning';
      default: return 'default';
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'info': return 'info';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1400, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          🗄️ Admin Retention Management
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Manage timeline data retention, archiving, and compliance policies
        </Typography>
      </Box>

      {/* Storage Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <StorageIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Storage Usage</Typography>
              </Box>
              <Typography variant="h4" color="primary.main" gutterBottom>
                {retentionStats.storageUsed}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                of {retentionStats.storageLimit} used
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={(2.3 / 10) * 100} 
                sx={{ height: 8, borderRadius: 4, mb: 1 }}
              />
              <Typography variant="caption" color="text.secondary">
                23% capacity used
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AssessmentIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Event Statistics</Typography>
              </Box>
              <Typography variant="h4" color="success.main" gutterBottom>
                {retentionStats.totalEvents.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Total timeline events
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                <Chip label={`${retentionStats.archivedEvents.toLocaleString()} archived`} size="small" />
                <Chip label={`${(retentionStats.totalEvents - retentionStats.archivedEvents).toLocaleString()} active`} size="small" color="primary" />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SecurityIcon color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">Compliance</Typography>
              </Box>
              <Typography variant="h4" color="warning.main" gutterBottom>
                {retentionStats.retentionPeriod}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Current retention period
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Oldest event: {retentionStats.oldestEvent}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Alerts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Alert severity="warning">
            <AlertTitle>Upcoming Automatic Actions</AlertTitle>
            156 events will be archived in 5 days. 23 events scheduled for deletion in 10 days.
          </Alert>
        </Grid>
        <Grid item xs={12} md={6}>
          <Alert severity="info">
            <AlertTitle>Storage Optimization</AlertTitle>
            Consider archiving older events to free up 0.8 GB of storage space.
          </Alert>
        </Grid>
      </Grid>

      {/* Retention Policies and Upcoming Actions */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Retention Policies */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Retention Policies</Typography>
                <Button 
                  variant="outlined" 
                  startIcon={<SettingsIcon />}
                  onClick={() => setOpenDialog(true)}
                >
                  Configure
                </Button>
              </Box>
              
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Policy Name</TableCell>
                      <TableCell>Archive After</TableCell>
                      <TableCell>Delete After</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Affected Events</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {retentionPolicies.map((policy) => (
                      <TableRow key={policy.id}>
                        <TableCell>
                          <Typography variant="subtitle2">{policy.name}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {policy.description}
                          </Typography>
                        </TableCell>
                        <TableCell>{policy.archiveAfter}</TableCell>
                        <TableCell>{policy.deleteAfter}</TableCell>
                        <TableCell>
                          <Chip 
                            label={policy.status} 
                            size="small" 
                            color={getStatusColor(policy.status)}
                          />
                        </TableCell>
                        <TableCell>{policy.affectedEvents.toLocaleString()}</TableCell>
                        <TableCell>
                          <IconButton size="small">
                            <ViewIcon />
                          </IconButton>
                          <IconButton size="small">
                            <SettingsIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Upcoming Actions */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Upcoming Actions
              </Typography>
              {upcomingActions.map((action, index) => (
                <Card key={index} variant="outlined" sx={{ mb: 2 }}>
                  <CardContent sx={{ pb: '16px !important' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle2">
                        {action.action}
                      </Typography>
                      <Chip 
                        label={action.type} 
                        size="small" 
                        color={getSeverityColor(action.severity)}
                      />
                    </Box>
                    <Typography variant="h6" color={`${getSeverityColor(action.severity)}.main`}>
                      {action.count} events
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Scheduled: {action.date}
                    </Typography>
                  </CardContent>
                </Card>
              ))}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Storage Breakdown */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Storage Breakdown
          </Typography>
          <Grid container spacing={3}>
            {storageBreakdown.map((item, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" color="primary.main">
                    {item.size}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {item.category}
                  </Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={item.percentage} 
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                  <Typography variant="caption" color="text.secondary">
                    {item.percentage}% of total
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Configuration Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Configure Retention Policy</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Policy Template</InputLabel>
                <Select
                  value={selectedPolicy}
                  label="Policy Template"
                  onChange={(e) => setSelectedPolicy(e.target.value)}
                >
                  <MenuItem value="standard">Standard Retention</MenuItem>
                  <MenuItem value="compliance">Compliance Retention</MenuItem>
                  <MenuItem value="short">Short-term Retention</MenuItem>
                  <MenuItem value="custom">Custom Policy</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Archive After (days)"
                type="number"
                defaultValue={365}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Delete After (days)"
                type="number"
                defaultValue={1095}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={autoArchive}
                    onChange={(e) => setAutoArchive(e.target.checked)}
                  />
                }
                label="Enable Automatic Archiving"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Policy Description"
                multiline
                rows={3}
                placeholder="Describe the purpose and scope of this retention policy..."
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => setOpenDialog(false)}>
            Save Policy
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminRetentionMockup;

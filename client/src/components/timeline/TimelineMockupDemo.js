import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Tabs,
  Tab,
  Button,
  Chip,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  Timeline as TimelineIcon,
  Feed as FeedIcon,
  Dashboard as DashboardIcon,
  AdminPanelSettings as AdminIcon,
  Visibility as ViewIcon,
  Code as CodeIcon,
  Speed as SpeedIcon,
  People as PeopleIcon
} from '@mui/icons-material';

// Import our mockup components
import TimelineVisualizationMockup from './TimelineVisualizationMockup';
import ActivityFeedMockup from './ActivityFeedMockup';
import TimelineStatsDashboard from './TimelineStatsDashboard';
import AdminRetentionMockup from './AdminRetentionMockup';

const TimelineMockupDemo = () => {
  const [activeTab, setActiveTab] = useState(0);

  const components = [
    {
      id: 'timeline-viz',
      name: 'Timeline Visualization',
      icon: <TimelineIcon />,
      description: 'Interactive timeline showing requirement history with filtering and different view modes',
      features: [
        'Chronological event display',
        'Multiple view modes (compact, detailed, grouped)',
        'Advanced filtering and search',
        'Version tracking',
        'User activity tracking',
        'Export capabilities'
      ],
      useCases: [
        'Requirement audit trails',
        'Change history review',
        'Compliance documentation',
        'Project timeline analysis'
      ],
      component: <TimelineVisualizationMockup />
    },
    {
      id: 'activity-feed',
      name: 'Activity Feed',
      icon: <FeedIcon />,
      description: 'Real-time activity stream with notifications, filtering, and social features',
      features: [
        'Real-time activity updates',
        'Notification system',
        'Activity categorization',
        'User mentions and tagging',
        'Priority indicators',
        'Social interactions (starring, following)'
      ],
      useCases: [
        'Team collaboration',
        'Real-time project monitoring',
        'Notification center',
        'Activity dashboard'
      ],
      component: <ActivityFeedMockup />
    },
    {
      id: 'stats-dashboard',
      name: 'Statistics Dashboard',
      icon: <DashboardIcon />,
      description: 'Analytics and insights for timeline activity across all projects',
      features: [
        'Activity trends and metrics',
        'User contribution analytics',
        'Project progress tracking',
        'Performance indicators',
        'Milestone tracking',
        'Customizable time ranges'
      ],
      useCases: [
        'Project management insights',
        'Team performance analysis',
        'Resource allocation',
        'Progress reporting'
      ],
      component: <TimelineStatsDashboard />
    },
    {
      id: 'admin-retention',
      name: 'Admin Retention Management',
      icon: <AdminIcon />,
      description: 'Manage timeline data retention, archiving, and compliance policies',
      features: [
        'Retention policy management',
        'Automatic archiving',
        'Storage monitoring',
        'Compliance tracking',
        'Data lifecycle management',
        'Audit trail preservation'
      ],
      useCases: [
        'Data governance',
        'Compliance management',
        'Storage optimization',
        'Legal requirements'
      ],
      component: <AdminRetentionMockup />
    }
  ];

  const currentComponent = components[activeTab];

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Header */}
      <Paper sx={{ p: 3, mb: 3, borderRadius: 0 }}>
        <Typography variant="h3" gutterBottom sx={{ fontWeight: 600 }}>
          🎨 Timeline UI Components Demo
        </Typography>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          Interactive mockups showing different approaches to timeline visualization
        </Typography>
        
        {/* Quick Stats */}
        <Grid container spacing={2} sx={{ mt: 2 }}>
          <Grid item>
            <Chip 
              icon={<ViewIcon />} 
              label="4 Component Options" 
              color="primary" 
              variant="outlined" 
            />
          </Grid>
          <Grid item>
            <Chip 
              icon={<SpeedIcon />} 
              label="Performance Optimized" 
              color="success" 
              variant="outlined" 
            />
          </Grid>
          <Grid item>
            <Chip 
              icon={<PeopleIcon />} 
              label="Multi-User Support" 
              color="info" 
              variant="outlined" 
            />
          </Grid>
          <Grid item>
            <Chip 
              icon={<CodeIcon />} 
              label="Material-UI Based" 
              color="secondary" 
              variant="outlined" 
            />
          </Grid>
        </Grid>
      </Paper>

      {/* Component Tabs */}
      <Box sx={{ px: 3 }}>
        <Paper sx={{ mb: 3 }}>
          <Tabs 
            value={activeTab} 
            onChange={(e, newValue) => setActiveTab(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            {components.map((comp, index) => (
              <Tab 
                key={comp.id}
                icon={comp.icon}
                label={comp.name}
                iconPosition="start"
                sx={{ minHeight: 72, textTransform: 'none' }}
              />
            ))}
          </Tabs>
        </Paper>

        {/* Component Info */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {currentComponent.icon}
                  <Typography variant="h5" sx={{ ml: 1, fontWeight: 600 }}>
                    {currentComponent.name}
                  </Typography>
                </Box>
                <Typography variant="body1" color="text.secondary" paragraph>
                  {currentComponent.description}
                </Typography>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  Key Features
                </Typography>
                <List dense>
                  {currentComponent.features.map((feature, index) => (
                    <ListItem key={index} sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Box 
                          sx={{ 
                            width: 8, 
                            height: 8, 
                            borderRadius: '50%', 
                            bgcolor: 'primary.main' 
                          }} 
                        />
                      </ListItemIcon>
                      <ListItemText primary={feature} />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Use Cases
                </Typography>
                <List dense>
                  {currentComponent.useCases.map((useCase, index) => (
                    <ListItem key={index} sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Box 
                          sx={{ 
                            width: 6, 
                            height: 6, 
                            borderRadius: '50%', 
                            bgcolor: 'success.main' 
                          }} 
                        />
                      </ListItemIcon>
                      <ListItemText 
                        primary={useCase}
                        primaryTypographyProps={{ variant: 'body2' }}
                      />
                    </ListItem>
                  ))}
                </List>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle2" gutterBottom>
                  Implementation Notes
                </Typography>
                <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                  • Built with Material-UI components
                </Typography>
                <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                  • Responsive design for all screen sizes
                </Typography>
                <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                  • Optimized for performance with large datasets
                </Typography>
                <Typography variant="caption" color="text.secondary" display="block">
                  • Accessible and keyboard navigable
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Component Demo */}
        <Paper sx={{ p: 0, overflow: 'hidden' }}>
          {currentComponent.component}
        </Paper>
      </Box>
    </Box>
  );
};

export default TimelineMockupDemo;

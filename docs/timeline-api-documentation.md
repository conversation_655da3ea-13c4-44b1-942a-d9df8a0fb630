# Timeline System API Documentation

## Overview

The Timeline System provides comprehensive tracking of all changes and activities related to requirements. It supports real-time event creation, filtering, statistics, and archival management.

## Base URL
All timeline endpoints are prefixed with `/api/timeline`

## Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer <your-jwt-token>
```

## Core Timeline Endpoints

### 1. Get Requirement Timeline

**GET** `/api/timeline/requirement/:requirementId`

Retrieves the timeline of events for a specific requirement.

**Parameters:**
- `requirementId` (path) - Requirement ObjectID or ElementID

**Query Parameters:**
- `includeArchived` (boolean) - Include archived events (default: false)
- `version` (number) - Filter by specific version
- `eventTypes` (string) - Comma-separated list of event types to include
- `limit` (number) - Maximum number of events to return
- `offset` (number) - Number of events to skip (default: 0)

**Example Request:**
```bash
GET /api/timeline/requirement/REQ-001?eventTypes=created,state_changed&limit=10
```

**Response:**
```json
[
  {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "requirementId": "64f8a1b2c3d4e5f6a7b8c9d1",
    "groupId": "64f8a1b2c3d4e5f6a7b8c9d2",
    "eventType": "created",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "user": {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d3",
      "username": "john.doe",
      "firstName": "John",
      "lastName": "Doe"
    },
    "version": 1,
    "eventData": {
      "title": "New Requirement"
    },
    "description": "Requirement created",
    "archived": false
  }
]
```

### 2. Get Requirement with Timeline

**GET** `/api/timeline/requirement/:requirementId/with-requirement`

Returns both the requirement details and its timeline in a single response.

**Response:**
```json
{
  "requirement": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d1",
    "elementId": "REQ-001",
    "title": "User Authentication",
    "currentVersion": 2,
    // ... other requirement fields
  },
  "timeline": [
    // ... timeline events array
  ]
}
```

### 3. Get Timeline Statistics

**GET** `/api/timeline/requirement/:requirementId/stats`

Returns statistical information about the requirement's timeline.

**Response:**
```json
{
  "totalEvents": 15,
  "eventTypes": [
    {
      "_id": "created",
      "count": 1,
      "lastEvent": "2024-01-15T10:30:00.000Z"
    },
    {
      "_id": "state_changed",
      "count": 5,
      "lastEvent": "2024-01-20T14:45:00.000Z"
    }
  ]
}
```

### 4. Get User Activity

**GET** `/api/timeline/user/:userId/activity`

Returns timeline events for a specific user across all requirements.

**Query Parameters:**
- `limit` (number) - Maximum number of events (default: 50)
- `offset` (number) - Number of events to skip

**Response:**
```json
[
  {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "requirementId": "64f8a1b2c3d4e5f6a7b8c9d1",
    "eventType": "comment_added",
    "timestamp": "2024-01-20T16:20:00.000Z",
    "description": "Comment added to requirement",
    "requirement": {
      "elementId": "REQ-001",
      "title": "User Authentication"
    }
  }
]
```

### 5. Get Project Timeline

**GET** `/api/timeline/project/:projectId`

Returns timeline events for all requirements within a project.

**Query Parameters:**
- Same as requirement timeline endpoint

### 6. Get Valid Event Types

**GET** `/api/timeline/event-types`

Returns all valid event types supported by the system.

**Response:**
```json
[
  "created",
  "version_created", 
  "state_changed",
  "tag_added",
  "tag_removed",
  "comment_added",
  "approval_given",
  "approval_removed",
  "member_added",
  "member_removed",
  "task_added",
  "task_completed",
  "test_result",
  "label_added",
  "label_removed",
  "document_generated"
]
```

## Admin Endpoints

### 7. Get Archival Statistics

**GET** `/api/timeline/admin/archival-stats`

Returns statistics about archived timeline events (Admin only).

**Response:**
```json
{
  "totalArchived": 1250,
  "byTier": [
    {
      "_id": "small",
      "count": 800
    },
    {
      "_id": "medium", 
      "count": 350
    }
  ],
  "oldestArchived": "2022-01-15T10:30:00.000Z",
  "newestArchived": "2023-12-31T23:59:59.000Z"
}
```

### 8. Get Retention Warnings

**GET** `/api/timeline/admin/retention-warnings`

Returns groups that need retention warnings (Admin only).

**Response:**
```json
{
  "warning": {
    "needsWarning": true,
    "daysUntilExpiration": 7,
    "expirationDate": "2024-02-01T00:00:00.000Z"
  },
  "recommendations": {
    "currentTier": "small",
    "currentRetentionMonths": 12,
    "usagePattern": "moderate"
  },
  "currentPolicy": {
    "tier": "small",
    "retentionMonths": 12,
    "archivalEnabled": true
  }
}
```

### 9. Purchase Extension

**POST** `/api/timeline/admin/purchase-extension`

Mock purchase of timeline retention extension (Admin only).

**Request Body:**
```json
{
  "extensionYears": 2
}
```

**Response:**
```json
{
  "success": true,
  "extensionYears": 2,
  "price": 198,
  "newExpirationDate": "2026-01-15T00:00:00.000Z",
  "transactionId": "mock_txn_1234567890"
}
```

### 10. Trigger Manual Archival

**POST** `/api/timeline/admin/trigger-archival`

Manually trigger archival process for a group (Admin only).

**Response:**
```json
{
  "archived": 45,
  "message": "Successfully archived 45 timeline events"
}
```

## Event Types Reference

| Event Type | Description | Typical Event Data |
|------------|-------------|-------------------|
| `created` | Requirement created | `{ title, description }` |
| `version_created` | New version created | `{ version, changes }` |
| `state_changed` | State transition | `{ from, to }` |
| `tag_added` | Tag added | `{ tag }` |
| `tag_removed` | Tag removed | `{ tag }` |
| `comment_added` | Comment added | `{ comment }` |
| `approval_given` | Approval granted | `{ approver }` |
| `approval_removed` | Approval revoked | `{ approver }` |
| `member_added` | Team member added | `{ member, role }` |
| `member_removed` | Team member removed | `{ member }` |
| `task_added` | Task created | `{ task }` |
| `task_completed` | Task completed | `{ task }` |
| `test_result` | Test result recorded | `{ result, details }` |
| `label_added` | Label added | `{ label }` |
| `label_removed` | Label removed | `{ label }` |
| `document_generated` | Document generated | `{ type, format }` |

## Error Responses

All endpoints return standard HTTP status codes:

- `200` - Success
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (requirement/resource not found)
- `500` - Internal Server Error

**Error Response Format:**
```

## Service Layer Documentation

For backend developers, the Timeline System provides service layer methods that can be used directly:

### TimelineService Methods

```javascript
const TimelineService = require('./services/TimelineService');

// Create a timeline event
const event = await TimelineService.addEvent(
  requirementId,    // ObjectId or string
  eventType,        // string (see Event Types above)
  userId,           // ObjectId
  groupId,          // ObjectId
  eventData,        // object (event-specific data)
  version           // number (requirement version)
);

// Get timeline for requirement
const timeline = await TimelineService.getRequirementTimeline(
  requirementId,    // ObjectId or ElementID
  groupId,          // ObjectId
  options           // { includeArchived, version, eventTypes, limit, offset }
);

// Get requirement with timeline
const result = await TimelineService.getRequirementWithTimeline(
  requirementId,    // ObjectId or ElementID
  groupId,          // ObjectId
  timelineOptions   // same as above
);

// Get user activity
const activity = await TimelineService.getUserActivity(
  userId,           // ObjectId
  groupId,          // ObjectId
  options           // { limit, offset }
);

// Get timeline statistics
const stats = await TimelineService.getTimelineStats(
  requirementId,    // ObjectId or ElementID
  groupId           // ObjectId
);
```

### ArchiveService Methods

```javascript
const ArchiveService = require('./services/ArchiveService');

// Check retention warnings for all groups
const warnings = await ArchiveService.checkAllGroups();

// Get retention recommendations for a group
const recommendations = await ArchiveService.getRetentionRecommendations(groupId);

// Mock purchase extension
const purchase = await ArchiveService.mockPurchaseMoreTime(groupId, extensionYears);

// Archive old events for a group
const result = await ArchiveService.archiveOldEvents(groupId);
```

### Integration Examples

#### Automatic Timeline Event Creation

Timeline events are automatically created when requirements are modified. To add timeline tracking to new features:

```javascript
// In your route handler
const TimelineService = require('../services/TimelineService');

// After creating/updating a requirement
await TimelineService.addEvent(
  requirement._id,
  'created',           // or appropriate event type
  req.user._id,
  req.userGroup._id,
  { title: requirement.title },  // relevant data
  requirement.currentVersion
);
```

#### Custom Event Types

To add new event types, update the TimelineEvent model:

```javascript
// In models/TimelineEvent.js
const validEventTypes = [
  // ... existing types
  'bug_reported',
  'corrective_action_created',
  'custom_event_type'
];
```

## Database Schema

### TimelineEvent Collection

```javascript
{
  _id: ObjectId,
  requirementId: ObjectId,      // Reference to requirement
  groupId: ObjectId,            // Multi-tenancy
  eventType: String,            // Event type (see reference)
  timestamp: Date,              // When event occurred
  user: ObjectId,               // User who triggered event
  version: Number,              // Requirement version
  eventData: Object,            // Event-specific data
  description: String,          // Human-readable description
  archived: Boolean             // Whether event is archived
}
```

### ArchivedTimelineEvent Collection

```javascript
{
  _id: ObjectId,
  originalEventId: ObjectId,    // Original timeline event ID
  originalRequirementId: ObjectId,
  groupId: ObjectId,
  archivedAt: Date,
  eventData: Object             // Complete original event data
}
```

### Group Retention Policy

```javascript
{
  retentionPolicy: {
    tier: String,               // 'small', 'medium', 'large', 'enterprise'
    retentionMonths: Number,    // 12, 24, 36, 60
    archivalEnabled: Boolean,   // Whether to archive old events
    archivalFrequency: String,  // 'monthly', 'weekly', 'daily'
    lastArchivalCheck: Date,
    extensionPurchases: [{
      purchaseDate: Date,
      extensionYears: Number,
      price: Number,
      transactionId: String
    }]
  }
}
```

## Performance Considerations

### Database Indexes

The system includes optimized indexes:

```javascript
// TimelineEvent indexes
{ requirementId: 1, groupId: 1, timestamp: -1 }
{ groupId: 1, eventType: 1, timestamp: -1 }
{ user: 1, groupId: 1, timestamp: -1 }
{ groupId: 1, archived: 1, timestamp: 1 }

// ArchivedTimelineEvent indexes
{ groupId: 1, archivedAt: -1 }
{ originalRequirementId: 1, groupId: 1 }
```

### Scaling Recommendations

- **Small Groups (1-3 users)**: 12-month retention, monthly archival
- **Medium Groups (4-25 users)**: 24-month retention, weekly archival
- **Large Groups (26-100 users)**: 36-month retention, weekly archival
- **Enterprise Groups (100+ users)**: 60-month retention, daily archival

### Memory Usage

- Active timeline events: ~1KB per event
- Archived events: ~500B per event (compressed)
- Typical group: 1000-5000 events per year

## Security

- All timeline data is filtered by group (multi-tenancy)
- Admin endpoints require admin role
- Timeline events cannot be deleted (only archived)
- User data is populated but sensitive fields are excluded

## Future Extensibility

The Timeline System is designed for easy extension:

1. **New Event Types**: Add to validEventTypes array
2. **Custom Data**: eventData field accepts any object structure
3. **New Endpoints**: Follow existing route patterns
4. **Real-time Updates**: WebSocket support can be added
5. **Advanced Analytics**: Timeline data supports complex queriesjson
{
  "message": "Error description",
  "error": "Detailed error information"
}
```

## Multi-Tenancy

All timeline data is automatically filtered by the user's group. Users can only access timeline events for requirements within their group.

## Performance Notes

- Timeline queries are optimized with database indexes
- Large timelines are paginated using `limit` and `offset`
- Archived events are stored separately for performance
- Real-time updates are supported via WebSocket (if implemented)

## Usage Examples

### Get Recent Activity for a Requirement
```javascript
const response = await fetch('/api/timeline/requirement/REQ-001?limit=10', {
  headers: { 'Authorization': `Bearer ${token}` }
});
const timeline = await response.json();
```

### Filter by Event Types
```javascript
const stateChanges = await fetch(
  '/api/timeline/requirement/REQ-001?eventTypes=state_changed,approval_given',
  { headers: { 'Authorization': `Bearer ${token}` } }
);
```

### Get Combined Data
```javascript
const combined = await fetch('/api/timeline/requirement/REQ-001/with-requirement', {
  headers: { 'Authorization': `Bearer ${token}` }
});
const { requirement, timeline } = await combined.json();
```
